name: Pull Request into `production` from `main`

on:
  push:
    branches:
      - main
  pull_request:
    types: [closed]
    branches:
      - main

jobs:
  auto-pull-request:
    if: github.repository == 'cheeaun/phanpy'
    runs-on: ubuntu-latest
    steps:
      - uses: vsoch/pull-request-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PULL_REQUEST_FROM_BRANCH: 'main'
          PULL_REQUEST_BRANCH: 'production'
