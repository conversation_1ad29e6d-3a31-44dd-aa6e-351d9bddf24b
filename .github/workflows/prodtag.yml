name: Auto-create tag/release on every push to `production`

on:
  push:
    branches:
      - production

jobs:
  tag:
    if: github.repository == 'cheeaun/phanpy'
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - uses: actions/checkout@v4
        with:
          ref: production
      # - run: git tag "`date +%Y.%m.%d`.`git rev-parse --short HEAD`" $(git rev-parse HEAD)
      # - run: git push --tags
      - uses: actions/setup-node@v3
        with:
          node-version: 20
      - run: npm ci && npm run build
      - run: cd dist && zip -r ../phanpy-dist.zip . && tar -czf ../phanpy-dist.tar.gz . && cd ..
      - id: tag_name
        run: echo ::set-output name=tag_name::$(date +%Y.%m.%d).$(git rev-parse --short HEAD)
      - uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ steps.tag_name.outputs.tag_name }}
          generate_release_notes: true
          files: |
            phanpy-dist.zip
            phanpy-dist.tar.gz
