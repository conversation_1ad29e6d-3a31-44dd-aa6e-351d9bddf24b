name: Playwright Tests
permissions:
  contents: read
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
jobs:
  test:
    timeout-minutes: 60
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*
      - name: Install dependencies
        run: npm ci
      - name: Install Playwright Browsers
        run: npx playwright install --with-deps webkit --only-shell
      - name: Run Playwright tests
        run: npm run test
      - uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
