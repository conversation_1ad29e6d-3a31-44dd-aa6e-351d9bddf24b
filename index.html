<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, viewport-fit=cover"
    />
    <title>%PHANPY_CLIENT_NAME%</title>
    <meta
      name="description"
      content="Minimalistic opinionated Mastodon web client"
    />
    <meta name="color-scheme" content="dark light" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <meta name="apple-mobile-web-app-title" content="%PHANPY_CLIENT_NAME%" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <link rel="canonical" href="%PHANPY_WEBSITE%" />
    <meta
      name=""
      data-theme-setting="manual"
      content="#242526"
      data-theme-light-color="#fff"
      data-theme-light-color-temp="#ffff"
      data-theme-dark-color="#242526"
      data-theme-dark-color-temp="#242526ff"
    />
    <meta
      name="theme-color"
      data-theme-setting="auto"
      content="#fff"
      data-content="#fff"
      data-content-temp="#fffa"
      media="(prefers-color-scheme: light)"
    />
    <meta
      name="theme-color"
      data-theme-setting="auto"
      content="#242526"
      data-content="#242526"
      data-content-temp="#242526aa"
      media="(prefers-color-scheme: dark)"
    />
    <meta name="google" content="notranslate" />
    <link rel="me" href="https://hachyderm.io/@phanpy" />

    <!-- Metacrap https://broken-links.com/2015/12/01/little-less-metacrap/ -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="og:url" content="%PHANPY_WEBSITE%" />
    <meta property="og:title" content="%PHANPY_CLIENT_NAME%" />
    <meta
      property="og:description"
      content="Minimalistic opinionated Mastodon web client"
    />
    <meta property="og:image" content="%PHANPY_WEBSITE%/og-image-2.jpg" />
  </head>
  <body>
    <div id="app"></div>
    <div id="modal-container"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
