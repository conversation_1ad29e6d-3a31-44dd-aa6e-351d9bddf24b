export const ICONS = {
  x: () => import('@iconify-icons/mingcute/close-line'),
  heart: () => import('@iconify-icons/mingcute/heart-line'),
  bookmark: () => import('@iconify-icons/mingcute/bookmark-line'),
  'check-circle': () => import('@iconify-icons/mingcute/check-circle-line'),
  'x-circle': () => import('@iconify-icons/mingcute/close-circle-line'),
  transfer: () => import('@iconify-icons/mingcute/transfer-4-line'),
  rocket: () => import('@iconify-icons/mingcute/rocket-line'),
  'arrow-left': {
    module: () => import('@iconify-icons/mingcute/arrow-left-line'),
    rtl: true,
  },
  'arrow-right': {
    module: () => import('@iconify-icons/mingcute/arrow-right-line'),
    rtl: true,
  },
  'arrow-up': () => import('@iconify-icons/mingcute/arrow-up-line'),
  'arrow-down': () => import('@iconify-icons/mingcute/arrow-down-line'),
  earth: () => import('@iconify-icons/mingcute/earth-line'),
  lock: () => import('@iconify-icons/mingcute/lock-line'),
  unlock: () => import('@iconify-icons/mingcute/unlock-line'),
  'eye-close': () => import('@iconify-icons/mingcute/eye-close-line'),
  'eye-open': () => import('@iconify-icons/mingcute/eye-2-line'),
  message: () => import('@iconify-icons/mingcute/mail-line'),
  comment: {
    module: () => import('@iconify-icons/mingcute/chat-3-line'),
    rtl: true,
  },
  comment2: {
    module: () => import('@iconify-icons/mingcute/comment-2-line'),
    rtl: true,
  },
  home: () => import('@iconify-icons/mingcute/home-3-line'),
  notification: () => import('@iconify-icons/mingcute/notification-line'),
  follow: () => import('@iconify-icons/mingcute/user-follow-line'),
  'follow-add': () => import('@iconify-icons/mingcute/user-add-line'),
  poll: [() => import('@iconify-icons/mingcute/chart-bar-line'), '90deg'],
  pencil: () => import('@iconify-icons/mingcute/pencil-line'),
  quill: () => import('@iconify-icons/mingcute/quill-pen-line'),
  at: () => import('@iconify-icons/mingcute/at-line'),
  attachment: () => import('@iconify-icons/mingcute/attachment-line'),
  upload: () => import('@iconify-icons/mingcute/upload-3-line'),
  gear: () => import('@iconify-icons/mingcute/settings-3-line'),
  more: () => import('@iconify-icons/mingcute/more-3-line'),
  more2: () => import('@iconify-icons/mingcute/more-1-fill'),
  external: {
    module: () => import('@iconify-icons/mingcute/external-link-line'),
    rtl: true,
  },
  popout: {
    module: () => import('@iconify-icons/mingcute/external-link-line'),
    rtl: true,
  },
  popin: {
    module: () => import('@iconify-icons/mingcute/external-link-line'),
    rotate: '180deg',
    rtl: true,
  },
  plus: () => import('@iconify-icons/mingcute/add-circle-line'),
  'chevron-left': {
    module: () => import('@iconify-icons/mingcute/left-line'),
    rtl: true,
  },
  'chevron-right': {
    module: () => import('@iconify-icons/mingcute/right-line'),
    rtl: true,
  },
  'chevron-down': () => import('@iconify-icons/mingcute/down-line'),
  reply: {
    module: () => import('@iconify-icons/mingcute/share-forward-line'),
    rotate: '180deg',
    flip: 'horizontal',
    rtl: true,
  },
  thread: () => import('@iconify-icons/mingcute/route-line'),
  group: {
    module: () => import('@iconify-icons/mingcute/group-line'),
    rtl: true,
  },
  bot: () => import('@iconify-icons/mingcute/android-2-line'),
  menu: () => import('@iconify-icons/mingcute/rows-4-line'),
  list: {
    module: () => import('@iconify-icons/mingcute/list-check-line'),
    rtl: true,
  },
  search: () => import('@iconify-icons/mingcute/search-2-line'),
  hashtag: () => import('@iconify-icons/mingcute/hashtag-line'),
  info: () => import('@iconify-icons/mingcute/information-line'),
  shortcut: () => import('@iconify-icons/mingcute/lightning-line'),
  user: () => import('@iconify-icons/mingcute/user-4-line'),
  following: () => import('@iconify-icons/mingcute/walk-line'),
  pin: () => import('@iconify-icons/mingcute/pin-line'),
  unpin: [() => import('@iconify-icons/mingcute/pin-line'), '180deg'],
  bus: () => import('@iconify-icons/mingcute/bus-2-line'),
  link: () => import('@iconify-icons/mingcute/link-2-line'),
  history: () => import('@iconify-icons/mingcute/history-line'),
  share: () => import('@iconify-icons/mingcute/share-2-line'),
  sparkles: () => import('@iconify-icons/mingcute/sparkles-line'),
  sparkles2: () => import('@iconify-icons/mingcute/sparkles-2-line'),
  exit: {
    module: () => import('@iconify-icons/mingcute/exit-line'),
    rtl: true,
  },
  translate: () => import('@iconify-icons/mingcute/translate-line'),
  play: () => import('@iconify-icons/mingcute/play-fill'),
  trash: () => import('@iconify-icons/mingcute/delete-2-line'),
  mute: {
    module: () => import('@iconify-icons/mingcute/volume-mute-line'),
    rtl: true,
  },
  unmute: {
    module: () => import('@iconify-icons/mingcute/volume-line'),
    rtl: true,
  },
  block: () => import('@iconify-icons/mingcute/forbid-circle-line'),
  unblock: [
    () => import('@iconify-icons/mingcute/forbid-circle-line'),
    '180deg',
  ],
  flag: () => import('@iconify-icons/mingcute/flag-1-line'),
  time: () => import('@iconify-icons/mingcute/time-line'),
  refresh: () => import('@iconify-icons/mingcute/refresh-2-line'),
  emoji2: () => import('@iconify-icons/mingcute/emoji-2-line'),
  filter: () => import('@iconify-icons/mingcute/filter-2-line'),
  filters: () => import('@iconify-icons/mingcute/filter-line'),
  chart: () => import('@iconify-icons/mingcute/chart-line-line'),
  react: () => import('@iconify-icons/mingcute/react-line'),
  layout4: {
    module: () => import('@iconify-icons/mingcute/layout-4-line'),
    rtl: true,
  },
  layout5: () => import('@iconify-icons/mingcute/layout-5-line'),
  announce: {
    module: () => import('@iconify-icons/mingcute/announcement-line'),
    rtl: true,
  },
  alert: () => import('@iconify-icons/mingcute/alert-line'),
  round: () => import('@iconify-icons/mingcute/round-fill'),
  'arrow-up-circle': () =>
    import('@iconify-icons/mingcute/arrow-up-circle-line'),
  'arrow-down-circle': () =>
    import('@iconify-icons/mingcute/arrow-down-circle-line'),
  clipboard: {
    module: () => import('@iconify-icons/mingcute/clipboard-line'),
    rtl: true,
  },
  'account-edit': () => import('@iconify-icons/mingcute/user-edit-line'),
  'account-warning': () => import('@iconify-icons/mingcute/user-warning-line'),
  'account-add': () => import('@iconify-icons/mingcute/user-add-2-line'),
  keyboard: () => import('@iconify-icons/mingcute/keyboard-line'),
  cloud: () => import('@iconify-icons/mingcute/cloud-line'),
  month: {
    module: () => import('@iconify-icons/mingcute/calendar-month-line'),
    rtl: true,
  },
  media: () => import('@iconify-icons/mingcute/photo-album-line'),
  speak: () => import('@iconify-icons/mingcute/radar-line'),
  building: () => import('@iconify-icons/mingcute/building-5-line'),
  history2: {
    module: () => import('@iconify-icons/mingcute/history-2-line'),
    rtl: true,
  },
  document: () => import('@iconify-icons/mingcute/document-line'),
  'arrows-right': {
    module: () => import('@iconify-icons/mingcute/arrows-right-line'),
    rtl: true,
  },
  code: () => import('@iconify-icons/mingcute/code-line'),
  copy: () => import('@iconify-icons/mingcute/copy-2-line'),
  quote: {
    module: () => import('@iconify-icons/mingcute/quote-left-line'),
    rtl: true,
  },
  settings: () => import('@iconify-icons/mingcute/settings-6-line'),
  'heart-break': () => import('@iconify-icons/mingcute/heart-crack-line'),
  'user-x': () => import('@iconify-icons/mingcute/user-x-line'),
  minimize: () => import('@iconify-icons/mingcute/arrows-down-line'),
  celebrate: () => import('@iconify-icons/mingcute/celebrate-line'),
  schedule: () => import('@iconify-icons/mingcute/calendar-time-add-line'),
  day: () => import('@iconify-icons/mingcute/calendar-day-line'),
  camera: () => import('@iconify-icons/mingcute/camera-line'),
  endorsement: {
    module: () => import('@iconify-icons/mingcute/user-star-line'),
    rtl: true,
  },
};
