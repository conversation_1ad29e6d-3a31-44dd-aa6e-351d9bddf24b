.avatar {
  display: inline-block;
  line-height: 0;
  aspect-ratio: 1 / 1;
  border-radius: 50%;
  overflow: hidden;
  background-color: var(--bg-faded-color);
  box-shadow: 0 0 0 1px var(--bg-blur-color);
  flex-shrink: 0;
  vertical-align: middle;

  &.has-alpha {
    border-radius: 0;
    background-color: transparent;
    box-shadow: none;

    img {
      background-color: transparent;
    }
  }
  &:not(.has-alpha).squircle {
    border-radius: 25%;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background-color: var(--img-bg-color);
    contain: none;
  }

  &[data-loaded],
  &[data-loaded] img {
    box-shadow: none;
    background-color: transparent;
  }
}
