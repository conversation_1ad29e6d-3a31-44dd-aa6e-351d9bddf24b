.account-block {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-color);
  text-decoration: none;

  .account-block-acct {
    display: inline-block;
  }
}
.account-block:hover b {
  text-decoration: underline;
}

.account-block.skeleton {
  color: var(--bg-faded-color);
}

.account-block .verified-field {
  display: inline-flex;
  align-items: baseline;
  gap: 2px;

  * {
    -webkit-box-orient: vertical;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    text-overflow: ellipsis;
    overflow: hidden;
    unicode-bidi: isolate;
    direction: initial;
  }

  a {
    pointer-events: none;
    color: color-mix(
      in lch,
      var(--green-color) 20%,
      var(--text-insignificant-color) 80%
    ) !important;
  }

  .icon {
    color: var(--green-color);
    transform: translateY(1px);
  }

  .invisible {
    display: none;
  }
  .ellipsis:after {
    content: '…';
  }
}

.account-block .account-block-stats {
  line-height: 1.25;
  margin-top: 2px;
  font-size: 0.9em;
  color: var(--text-insignificant-color);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  column-gap: 4px;

  a {
    color: inherit;
    text-decoration: none;
  }
}
