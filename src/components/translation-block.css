.status-translation-block {
  margin: 8px 0 0;
  padding: 0;
  font-size: 90%;
  border-radius: 8px;
}
.status-translation-block summary {
  list-style: none;
  display: inline-block;
}
.status-translation-block summary::-webkit-details-marker {
  display: none;
}
.status-translation-block summary button {
  border-radius: 8px;
  border: 1px solid var(--outline-color);
  padding: 8px;
  background-color: var(--bg-color);
  font-size: 12px;
  color: var(--text-insignificant-color);
}
.status-translation-block summary button:is(:hover, :focus) {
  color: var(--text-color);
  filter: none !important;
}
.status-translation-block details:not([open]) .detected {
  display: none;
}
/* .status-translation-block details summary button:active, */
.status-translation-block details[open] summary button {
  /* color: var(--text-color); */
  /* background-color: var(--bg-faded-color); */
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom: 0;
  margin-bottom: -1px;
  background-image: linear-gradient(
    to top var(--backward),
    var(--bg-color) 50%,
    var(--bg-faded-blur-color)
  );
  box-shadow: inset 0 0 0 1px var(--bg-color);
}
.status-translation-block .translated-block {
  border: 1px solid var(--outline-color);
  line-height: 1.3;
  border-radius: 8px;
  border-start-start-radius: 0;
  margin: 0;
  padding: 8px;
  background-color: var(--bg-color);
  background-image: linear-gradient(
    to bottom var(--forward),
    var(--bg-color),
    var(--bg-faded-blur-color)
  );
  white-space: pre-wrap;
  box-shadow:
    inset 0 0 0 1px var(--bg-color),
    0 1px 5px -2px var(--drop-shadow-color);
  text-shadow: 0 1px var(--bg-color);
}
.status-translation-block .translated-block .translation-info {
  display: flex;
  align-items: center;
  gap: 8px;
}
.status-translation-block .translated-block .translation-info * {
  vertical-align: middle;
  flex-shrink: 0;
}
.status-translation-block .translated-source-select {
  appearance: none;
  display: inline-block;
  margin: 0;
  padding: 4px 8px;
  border: 0;
  border-radius: 8px;
  background-color: var(--bg-faded-color);
  color: inherit;
  width: min-content;
  min-width: 2em;
  flex-shrink: 1 !important;
}
.status-translation-block .translated-block output {
  display: block;
  margin-top: 0.75em;
  text-wrap: pretty;
}
.status-translation-block
  .translated-block
  output.translated-pronunciation-content {
  opacity: 0.75;
  padding-top: 0.75em;
  border-top: var(--hairline-width) solid var(--outline-color);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  mask-image: linear-gradient(to bottom, black 3em, transparent);
}
.status-translation-block
  .translated-block
  output.translated-pronunciation-content.expand {
  display: block;
  -webkit-line-clamp: unset;
  -webkit-box-orient: unset;
  overflow: visible;
  mask-image: none;
}

/* MINI */

.status-translation-block-mini {
  display: flex;
  margin: 8px 0 0;
  font-size: 90%;
  gap: 8px;
  --top-padding: 2px;
  --top-border: 1px;

  .icon {
    color: var(--text-insignificant-color);
    margin-top: calc(var(--top-padding) + var(--top-border) + 2px);
  }

  output {
    padding: var(--top-padding) 0 0;
    border-top: var(--top-border) dashed var(--outline-stronger-color);
    text-wrap: pretty;
  }
}
