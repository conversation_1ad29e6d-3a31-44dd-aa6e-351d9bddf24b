#generic-accounts-container {
  .post-preview {
    --max-height: 120px;
    max-height: var(--max-height);
    overflow: hidden;
    margin-block: 8px;
    border: 1px solid var(--outline-color);
    border-radius: 8px;
    pointer-events: none;

    .status {
      font-size: calc(var(--text-size) * 0.9);
      mask-image: linear-gradient(
        to bottom,
        black calc(var(--max-height) / 2),
        transparent calc(var(--max-height) - 8px)
      );
      filter: saturate(0.5);
    }

    &:is(a) {
      pointer-events: auto;
      display: block;
      text-decoration: none;
      color: inherit;

      &:hover {
        border-color: var(--outline-hover-color);
      }

      > * {
        pointer-events: none;
      }
    }
  }

  .accounts-list {
    --list-gap: 16px;
    list-style: none;
    margin: 0;
    padding: 8px 0;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    column-gap: 1.5em;
    row-gap: var(--list-gap);

    li {
      display: flex;
      flex-grow: 1;
      flex-basis: 16em;
      /* align-items: center; */
      margin: 0;
      padding: 0;
      gap: 8px;

      position: relative;

      &:before {
        content: '';
        display: block;
        border-top: var(--hairline-width) solid var(--divider-color);
        position: absolute;
        bottom: calc(-1 * var(--list-gap) / 2);
        inset-inline-start: 40px;
        inset-inline-end: 0;
      }

      &:has(.reactions-block):before {
        /* avatar + reactions + gap */
        inset-inline-start: calc(40px + 16px + 8px);
      }
    }

    .account-block-acct {
      font-size: 0.9em;
      color: var(--text-insignificant-color);
      /* display: block; */
    }
  }

  .reactions-block {
    display: flex;
    flex-direction: column;
    /* align-self: center; */

    .favourite-icon {
      color: var(--favourite-color);
    }

    .reblog-icon {
      color: var(--reblog-color);
    }

    > .icon:only-child {
      margin-top: 8px; /* half of icon dimension */
    }
  }

  .account-relationships {
    flex-grow: 1;

    .tag {
      animation: appear 0.3s ease-out;
    }
  }

  .account-block {
    align-items: flex-start;
  }
}
