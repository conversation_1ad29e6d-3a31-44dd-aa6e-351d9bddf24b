#modal-container > div {
  position: fixed;
  top: 0;
  inset-inline-end: 0;
  height: 100%;
  width: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--backdrop-color);
  animation: appear 0.5s var(--timing-function) both;
  transition: all 0.5s var(--timing-function);

  &.solid {
    background-color: var(--backdrop-solid-color);
  }

  --compose-button-dimension: 56px;
  --compose-button-dimension-half: calc(var(--compose-button-dimension) / 2);
  --compose-button-dimension-margin: 16px;

  &.min {
    /* Minimized */
    pointer-events: none;
    user-select: none;
    overflow: hidden;
    transform: scale(0);
    --end: max(
      var(--compose-button-dimension-margin),
      env(safe-area-inset-right)
    );
    :dir(rtl) & {
      --end: max(
        var(--compose-button-dimension-margin),
        env(safe-area-inset-left)
      );
    }
    --bottom: max(
      var(--compose-button-dimension-margin),
      env(safe-area-inset-bottom)
    );
    --origin-end: calc(100% - var(--compose-button-dimension-half) - var(--end));
    :dir(rtl) & {
      --origin-end: calc(var(--compose-button-dimension-half) + var(--end));
    }
    --origin-bottom: calc(
      100% -
      var(--compose-button-dimension-half) -
      var(--bottom)
    );
    transform-origin: var(--origin-end) var(--origin-bottom);
  }

  .sheet {
    transition: transform 0.3s var(--timing-function);
    transform-origin: 80% 80%;
  }

  &:has(~ div) .sheet {
    transform: scale(0.975);
  }
}

@media (max-width: calc(40em - 1px)) {
  #app[data-shortcuts-view-mode='tab-menu-bar'] ~ #modal-container > div.min {
    border: 2px solid red;

    --bottom: calc(
      var(--compose-button-dimension-margin) +
      env(safe-area-inset-bottom) +
      52px
    );
  }
}
