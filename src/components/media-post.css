.media-post {
  --item-radius: 16px;
  position: relative;
  animation: appear-smooth 1s ease-out;

  &:is(.filtered, .has-spoiler:not(.show-media)) :is(img, video) {
    /* filter: blur(32px);
    image-rendering: crisp-edges;
    image-rendering: pixelated; */
    opacity: 0;
    animation: none !important;
  }

  &.filtered[data-filtered-text]:before {
    content: attr(data-filtered-text);
  }
  &.has-spoiler[data-spoiler-text]:before {
    content: attr(data-spoiler-text);
  }

  &.filtered[data-filtered-text]:before,
  &.has-spoiler[data-spoiler-text]:before {
    pointer-events: none;
    position: absolute;
    top: 0;
    inset-inline-start: 0;
    z-index: 1;
    background-color: var(--bg-blur-color);
    margin: 8px;
    padding: 4px 6px;
    border-radius: calc(var(--item-radius) / 2);
    font-size: 90%;
    border: var(--hairline-width) dashed var(--bg-color);
    word-break: break-word;
    word-wrap: break-word;
    overflow-wrap: break-word;
    /* mix-blend-mode: luminosity; */
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    box-orient: vertical;
    display: -webkit-box;
    display: box;
    overflow: hidden;
    z-index: 2;

    > * {
      pointer-events: none;
    }
  }

  &.has-spoiler.show-media[data-spoiler-text]:before {
    mix-blend-mode: normal;
    backdrop-filter: blur(4px);
  }

  .media {
    border-radius: var(--item-radius);
    overflow: hidden;
    position: relative;
    display: block;
    aspect-ratio: 1 !important;

    &:before {
      position: absolute;
      inset: 0;
      content: '';
      border: 1px solid var(--outline-color);
      border-radius: inherit;
    }

    &:not(.media-audio) {
      background-color: var(--average-color, var(--media-bg-color));
      background-clip: padding-box;
    }

    @media (hover: hover) {
      &:hover {
        --drop-shadow: var(--drop-shadow-color);
        position: relative;
        z-index: 1;
        box-shadow:
          0 8px 16px -4px var(--drop-shadow),
          0 4px 8px var(--drop-shadow);

        @media (prefers-color-scheme: dark) {
          --drop-shadow: var(--link-color);
        }
      }
    }

    &:active:not(:has(button:active)) {
      box-shadow: none;
      filter: brightness(0.8);
      transform: scale(0.99);
    }

    video,
    img,
    audio {
      border-radius: 16px;
      /* object-fit: scale-down; */
      object-fit: cover;
      width: 100%;
      height: 100%;
      vertical-align: top;
    }

    :not(.filtered, .has-spoiler) &:is(:hover, :focus) img {
      /* Less delay here to make it feel more responsive */
      animation: position-object 5s ease-in-out 0.1s 5;
      animation-duration: var(--anim-duration, 5s);
    }
  }

  &.has-spoiler .media:not(.media-audio) {
    background-image: radial-gradient(
      circle at 50% 50%,
      var(--average-color, var(--bg-faded-color)),
      var(--bg-color) 20em
    );
  }
}
