#compose-container-outer {
  width: 100%;
  height: 100vh;
  height: 100dvh;
  overflow: auto;
  align-self: flex-start;
  padding: env(safe-area-inset-top) env(safe-area-inset-right)
    env(safe-area-inset-bottom) env(safe-area-inset-left);
}
#compose-container {
  margin: auto;
  width: var(--main-width);
  max-width: 100vw;
  align-self: stretch;
  animation: fade-in 0.2s ease-out;
}

#compose-container .compose-top {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  align-items: center;
  padding: 8px;
  position: sticky;
  top: 0;
  z-index: 100;
  white-space: nowrap;

  @media (min-width: 480px) {
    padding: 16px;
  }
}
#compose-container .compose-top .account-block {
  text-align: start;
  pointer-events: none;
  overflow: hidden;
  color: var(--text-insignificant-color);
  line-height: 1.1;
  font-size: 90%;
  background-color: var(--bg-faded-blur-color);
  backdrop-filter: blur(16px);
  padding-inline-end: 1em;
  border-radius: 9999px;
}

@keyframes appear-up {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
#compose-container .status-preview {
  border-radius: 16px 16px 0 0;
  max-height: 160px;
  background-color: var(--bg-color);
  margin: 0 12px;
  border: 1px solid var(--outline-color);
  border-bottom: 0;
  animation: appear-up 1s ease-in-out;
  overflow: auto;
  box-shadow: 0 -3px 12px -3px var(--drop-shadow-color);
}
#compose-container .status-preview:has(.status-badge:not(:empty)) {
  border-start-end-radius: 8px;
}
#compose-container .status-preview :is(.content-container, .time) {
  pointer-events: none;
}
#compose-container.standalone .status-preview * {
  /*
    For standalone mode (new window), prevent interacting with the status preview for now
  */
  pointer-events: none;
}

#compose-container .status-preview-legend {
  pointer-events: none;
  position: sticky;
  bottom: 0;
  padding: 8px;
  font-size: 80%;
  font-weight: bold;
  text-align: center;
  color: var(--text-insignificant-color);
  background-color: var(--bg-blur-color);
  /* background-image: linear-gradient(
    to bottom,
    transparent,
    var(--bg-faded-color)
  ); */
  border-top: var(--hairline-width) solid var(--outline-color);
  backdrop-filter: blur(8px);
  text-shadow:
    0 1px 10px var(--bg-color),
    0 1px 10px var(--bg-color),
    0 1px 10px var(--bg-color),
    0 1px 10px var(--bg-color),
    0 1px 10px var(--bg-color);
  z-index: 2;

  strong {
    color: var(--red-color);
  }
}
#_compose-container .status-preview-legend.reply-to {
  color: var(--reply-to-color);
  background-color: var(--reply-to-faded-color);
  /* background-image: linear-gradient(
    to bottom,
    transparent,
    var(--reply-to-faded-color)
  ); */
}

#compose-container form {
  --form-spacing-inline: 4px;
  --form-spacing-block: 0;
  /* border-radius: 16px; */
  padding: var(--form-spacing-block) var(--form-spacing-inline);
  background-color: var(--bg-blur-color);
  /* background-image: linear-gradient(var(--bg-color) 85%, transparent); */
  position: relative;
  z-index: 2;
  --drop-shadow: 0 3px 6px -3px var(--drop-shadow-color);
  box-shadow: var(--drop-shadow);

  @media (min-width: 480px) {
    --form-spacing-inline: 8px;
  }

  @media (min-width: 40em) {
    border-radius: 16px;
  }
}
#compose-container .status-preview ~ form {
  box-shadow:
    var(--drop-shadow),
    0 -3px 6px -3px var(--drop-shadow-color);
}

#compose-container textarea {
  width: 100%;
  max-width: 100%;
  height: 5em;
  min-height: 5em;
  max-height: 50vh;
  resize: vertical;
  line-height: 1.4;
  border-color: transparent;

  &.compose-field {
    @media (min-width: 40em) {
      max-height: 65vh;
    }
  }
}
#compose-container textarea:hover {
  border-color: var(--divider-color);
}

#compose-container .toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--form-spacing-inline) 0;
  gap: var(--form-spacing-inline);
}
#compose-container .toolbar.wrap {
  flex-wrap: wrap;
}
#compose-container .toolbar.stretch {
  justify-content: stretch;
}
#compose-container .toolbar .spoiler-text-field {
  flex: 1;
  min-width: 0;
}
#compose-container .toolbar-button {
  display: inline-block;
  color: var(--link-color);
  background-color: transparent;
  padding: 0 8px;
  border-radius: 8px;
  min-height: 2.4em;
  line-height: 2.4em;
  min-width: 2.6em;
  text-align: center;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
  border: 2px solid transparent;
  vertical-align: middle;

  &.active {
    filter: brightness(0.8);
    background-color: var(--bg-color);
  }
}
#compose-container .toolbar-button > * {
  vertical-align: middle;
  cursor: inherit;
  outline: 0;
}
#compose-container .toolbar-button:has([disabled]),
#compose-container .toolbar-button[disabled] {
  pointer-events: none;
  background-color: transparent;
  opacity: 0.5;
}
#compose-container
  .toolbar-button:not(.show-field)
  :is(input[type='checkbox'], select, input[type='file']) {
  opacity: 0;
  position: absolute;
  left: 0;
  height: 100%;
  margin: 0;
}
#compose-container .toolbar-button input[type='file'] {
  /* Move this out of the way, to fix cursor: pointer bug */
  left: -100vw !important;
}
#compose-container .toolbar-button select {
  background-color: inherit;
  border: 0;
  padding: 0 0 0 8px;
  margin: 0;
  appearance: none;
  line-height: 1em;
}
#compose-container .toolbar-button:not(.show-field) select {
  inset-inline-end: 0;
  inset-inline-start: auto !important;
}
#compose-container
  .toolbar-button:not(:disabled):is(
    :hover,
    :focus,
    :focus-within,
    :focus-visible
  ) {
  cursor: pointer;
  filter: none;
  background-color: var(--bg-color);
  border-color: var(--link-faded-color);
  outline: 0;
}
#compose-container .toolbar-button:not(:disabled).highlight {
  border-color: var(--link-color);
  box-shadow: inset 0 0 8px var(--link-faded-color);
}
#compose-container .toolbar-button:not(:disabled):active {
  filter: brightness(0.8);
}

#compose-container .toolbar-button .icon-text {
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
  min-width: 4ch;
}

#compose-container .compose-footer {
  .add-toolbar-button-group {
    display: flex;
    overflow: auto;
  }
  .add-sub-toolbar-button-group {
    flex-grow: 1;
    display: flex;
    overflow: auto;
    transition: 0.5s ease-in-out;
    transition-property: opacity, width;
    scrollbar-width: none;
    padding-inline-end: 16px;
    mask-image: linear-gradient(
      var(--to-backward),
      transparent 0,
      black 16px,
      black 100%
    );

    &::-webkit-scrollbar {
      display: none;
    }

    &[hidden] {
      opacity: 0;
      pointer-events: none;
      width: 0;
    }
  }
}

#compose-container text-expander {
  position: relative;
  display: block;
}
#compose-container .text-expander-menu {
  color: var(--text-color);
  background-color: var(--bg-color);
  position: absolute;
  margin-top: 2em;
  padding: 0;
  list-style: none;
  border: 1px solid var(--outline-color);
  box-shadow: 0 4px 24px var(--drop-shadow-color);
  border-radius: 8px;
  overflow: hidden;
  z-index: 100;
  min-width: 10em;
  max-width: 90vw;
}
#compose-container .text-expander-menu li {
  white-space: nowrap;
  padding: 8px;
  cursor: pointer;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  gap: 8px;
  align-items: center;
  font-size: 90%;

  .grow {
    flex-grow: 1;
  }

  .count {
    font-size: 80%;
    opacity: 0.5;
  }
}
#compose-container .text-expander-menu li b img {
  /* The shortcode emojis */
  width: 0.9em;
  height: 0.9em;
}
#compose-container .text-expander-menu li .avatar {
  width: 2.2em;
  height: 2.2em;
}
#compose-container .text-expander-menu li:is(:hover, :focus, [aria-selected]) {
  background-color: var(--link-bg-color);
  color: var(--text-color);
}
#compose-container .text-expander-menu li[aria-selected] {
  box-shadow: inset 4px 0 0 0 var(--button-bg-color);
  :dir(rtl) & {
    box-shadow: inset -4px 0 0 0 var(--button-bg-color);
  }
}
#compose-container .text-expander-menu li[data-more] {
  &:not(:hover, :focus, [aria-selected]) {
    color: var(--text-insignificant-color);
    background-color: var(--bg-faded-color);
  }

  font-size: 0.8em;
  justify-content: center;
}

#compose-container .form-visibility-direct {
  --yellow-stripes: repeating-linear-gradient(
    135deg,
    var(--reply-to-faded-color),
    var(--reply-to-faded-color) 10px,
    var(--reply-to-faded-color) 10px,
    transparent 10px,
    transparent 20px
  );
  /* diagonal stripes of yellow */
  background-image: var(--yellow-stripes);
}

#compose-container .media-attachments {
  background-color: var(--bg-faded-color);
  padding: 8px;
  border-radius: 8px;
  margin: 8px 0 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
#compose-container .media-attachment {
  display: flex;
  gap: 8px;
  align-items: stretch;

  .media-error {
    padding: 2px;
    color: var(--orange-fg-color);
    background-color: transparent;
    border: 1.5px dashed transparent;
    line-height: 1;
    border-radius: 4px;
    display: flex;

    &:is(:hover, :focus) {
      background-color: var(--bg-color);
      border-color: var(--orange-fg-color);
    }
  }
}
#compose-container .media-preview {
  flex-shrink: 0;
  border: 1px solid var(--outline-color);
  border-radius: 4px;
  overflow: hidden;
  width: 80px;
  height: 80px;
  /* checkerboard background */
  background-image:
    linear-gradient(45deg, var(--img-bg-color) 25%, transparent 25%),
    linear-gradient(-45deg, var(--img-bg-color) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, var(--img-bg-color) 75%),
    linear-gradient(-45deg, transparent 75%, var(--img-bg-color) 75%);
  background-size: 10px 10px;
  background-position:
    0 0,
    0 5px,
    5px -5px,
    -5px 0px;
}
#compose-container .media-preview > * {
  width: 80px;
  height: 80px;
  object-fit: scale-down;
  vertical-align: middle;
  pointer-events: none;
}
#compose-container .media-preview:hover {
  box-shadow: 0 0 0 2px var(--link-light-color);
  cursor: pointer;
}
#compose-container .media-attachment textarea {
  height: 80px;
  flex-grow: 1;
  resize: none;
}
#compose-container .media-attachments .media-desc {
  flex-grow: 1;
}
#compose-container .media-attachments .media-desc p {
  font-size: 90%;
  margin: 0;
  padding: 0;
  /* clamp 2 lines */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
#compose-container .media-attachments .media-desc p i {
  color: var(--text-insignificant-color);
}
#compose-container .media-aside {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
#compose-container .media-aside .close-button {
  padding: 4px;
  align-self: flex-start;
  color: var(--text-insignificant-color);
}
#compose-container .media-aside .close-button:is(:hover, :focus) {
  color: var(--text-color);
}
#compose-container .media-aside .uploaded {
  color: var(--green-color);
  margin-bottom: 4px;
}

#compose-container .media-sensitive {
  padding: 8px;
  background-color: var(--bg-blur-color);
  border-radius: 8px;
  cursor: pointer;
}
#compose-container .media-sensitive > * {
  vertical-align: middle;
}

#compose-container form .poll {
  background-color: var(--bg-faded-color);
  border-radius: 8px;
  margin: 8px 0 0;
  display: block;
}

#compose-container .poll-choices {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
}
#compose-container .poll-choice {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: stretch;
  flex-direction: row-reverse;
}
#compose-container .poll-choice input {
  flex-grow: 1;
  min-width: 0;
}

#compose-container .poll-button {
  border: 2px solid var(--outline-color);
  width: 28px;
  height: 28px;
  padding: 0;
  flex-shrink: 0;
  line-height: 0;
  overflow: hidden;
  transition: border-radius 1s ease-out;
  font-size: 14px;
}
#compose-container .multiple .poll-button {
  border-radius: 4px;
}

#compose-container .poll-toolbar {
  display: flex;
  gap: 8px;
  align-items: stretch;
  justify-content: space-between;
  font-size: 90%;
  border-top: 1px solid var(--outline-color);
  padding: 8px;
}
#compose-container .poll-toolbar select {
  padding: 4px;
}

#compose-container .multiple-choices {
  flex-grow: 1;
  display: flex;
  gap: 4px;
  align-items: center;
  border-inline-start: 1px solid var(--outline-color);
  padding-inline-start: 8px;
}

#compose-container .expires-in {
  flex-grow: 1;
  border-inline-start: 1px solid var(--outline-color);
  padding-inline-start: 8px;
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
}

#compose-container .remove-poll-button {
  width: 100%;
  color: var(--red-color);
}

#compose-container {
  .scheduled-at {
    background-color: var(--bg-faded-color);
    border-radius: 8px;
    margin: 8px 0 0;
    justify-content: flex-end;
    text-align: end;

    input[type='datetime-local'] {
      max-width: 80vw;
      padding: 4px;

      &:invalid {
        border-color: var(--red-color);
      }
    }
  }
}

.compose-menu-add-media {
  position: relative;

  .compose-menu-add-media-field {
    position: absolute;
    inset: 0;
    opacity: 0;
    cursor: inherit;
  }
}

.icon-gif {
  display: inline-block !important;
  min-width: 16px;
  height: 16px;
  font-size: 10px !important;
  letter-spacing: -0.5px;
  font-size-adjust: none;
  overflow: hidden;
  white-space: nowrap;
  text-align: center;
  line-height: 16px;
  font-weight: bold;
  text-rendering: optimizeSpeed;

  &:after {
    display: block;
    content: 'GIF';
  }
}

@media (display-mode: standalone) {
  /* No popping in standalone mode */
  #compose-container .pop-button {
    display: none;
  }
}

#compose-container button[type='submit'] {
  border-radius: 8px;

  @media (min-width: 480px) {
    padding-inline: 24px;
    font-size: 125%;
  }
}

@keyframes breathe {
  0% {
    opacity: 1;
  }
  40% {
    opacity: 0.4;
  }
  100% {
    opacity: 1;
  }
}

#media-sheet {
  .media-form {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-height: 50vh;

    textarea {
      flex-grow: 1;
      resize: none;
      width: 100%;
      /* height: 10em; */

      &.loading {
        animation: skeleton-breathe 1.5s linear infinite;
      }
    }

    footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}
#media-sheet main {
  padding-top: 8px;
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 8px;
}
#media-sheet .media-preview {
  border: 2px solid var(--outline-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 16px var(--img-bg-color);
  /* checkerboard background */
  background-image:
    linear-gradient(45deg, var(--img-bg-color) 25%, transparent 25%),
    linear-gradient(-45deg, var(--img-bg-color) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, var(--img-bg-color) 75%),
    linear-gradient(-45deg, transparent 75%, var(--img-bg-color) 75%);
  background-size: 20px 20px;
  background-position:
    0 0,
    0 10px,
    10px -10px,
    -10px 0px;
  flex: 0.8;
}
#media-sheet .media-preview > * {
  width: 100%;
  height: 100%;
  max-height: 50vh;
  object-fit: scale-down;
  vertical-align: middle;
}

@media (min-width: 50em) {
  #media-sheet main {
    flex-direction: row;
  }
  #media-sheet .media-preview {
    flex: 2;
  }
  #media-sheet .media-preview > * {
    max-height: none;
  }
  /* #media-sheet textarea {
    flex: 1;
    min-height: 100%;
    height: auto;
  } */
}

#mention-sheet {
  height: 50vh;

  .accounts-list {
    --list-gap: 1px;
    list-style: none;
    margin: 0;
    padding: 8px 0;
    display: flex;
    flex-direction: column;
    row-gap: var(--list-gap);

    &.loading {
      opacity: 0.5;
    }

    li {
      display: flex;
      flex-grow: 1;
      /* align-items: center; */
      margin: 0 -8px;
      padding: 8px;
      gap: 8px;
      position: relative;
      justify-content: space-between;
      border-radius: 8px;
      /* align-items: center; */

      &:hover {
        background-image: linear-gradient(
          var(--to-forward),
          transparent 75%,
          var(--link-bg-color)
        );
      }

      &.selected {
        background-image: linear-gradient(
          var(--to-forward),
          var(--bg-faded-color) 75%,
          var(--link-bg-color)
        );
      }

      &:before {
        content: '';
        display: block;
        border-top: var(--hairline-width) solid var(--divider-color);
        position: absolute;
        bottom: 0;
        inset-inline-start: 58px;
        inset-inline-end: 0;
      }

      &:has(+ li:is(.selected, :hover)):before,
      &:is(.selected, :hover):before {
        opacity: 0;
      }

      > button {
        border-radius: 4px;
        &:hover {
          outline: 2px solid var(--button-bg-blur-color);
        }
      }
    }
  }
}

#custom-emojis-sheet {
  max-height: 50vh;
  max-height: 50dvh;

  header {
    .loader-container {
      margin: 0;
    }

    form {
      margin: 8px 0 0;

      input {
        width: 100%;
        min-width: 0;
      }
    }
  }

  main {
    mask-image: none;
    min-height: 40vh;
    padding-bottom: 88px;
  }

  .custom-emojis-matches {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
    flex-wrap: wrap;
  }

  .custom-emojis-list {
    .section-container {
      position: relative;
      content-visibility: auto;
      content-intrinsic-size: auto 88px;
    }
    .section-header {
      font-size: 80%;
      text-transform: uppercase;
      color: var(--text-insignificant-color);
      padding: 8px 0 4px;
      position: sticky;
      top: 0;
      background-color: var(--bg-color);
      z-index: 1;
      display: inline-block;
      padding-inline-end: 8px;
      pointer-events: none;
      border-end-end-radius: 8px;
    }
    section {
      display: flex;
      flex-wrap: wrap;
    }
    button {
      color: var(--text-color);
      border-radius: 8px;
      background-image: radial-gradient(
        closest-side,
        var(--img-bg-color),
        transparent
      );
      text-shadow: 0 1px 0 var(--bg-color);
      position: relative;
      min-width: 44px;
      min-height: 44px;
      font-variant-numeric: slashed-zero;
      font-feature-settings: 'ss01';

      &[data-title]:after {
        max-width: 50vw;
        pointer-events: none;
        position: absolute;
        content: attr(data-title);
        left: 50%;
        top: 0;
        background-color: var(--bg-color);
        padding: 2px 4px;
        border-radius: 4px;
        font-size: 12px;
        border: 1px solid var(--text-color);
        transform: translate(-50%, -110%);
        opacity: 0;
        transition: opacity 0.1s ease-out 0.1s;
        font-family: var(--monospace-font);
        line-height: 1;
      }
      &.edge-left[data-title]:after {
        left: 0;
        transform: translate(0, -110%);
      }
      &.edge-right[data-title]:after {
        left: 100%;
        transform: translate(-100%, -110%);
      }

      &:is(:hover, :focus) {
        z-index: 1;
        filter: none;
        background-color: var(--bg-faded-color);

        &[data-title]:after {
          opacity: 1;
        }
      }

      img {
        transition: transform 0.1s ease-out;
      }

      &:is(:hover, :focus) img {
        transform: scale(2);
      }
      &.edge-left img {
        transform-origin: left center;
      }
      &.edge-right img {
        transform-origin: right center;
      }

      code {
        font-size: 0.8em;
      }
    }
  }
}

.compose-field-container {
  display: grid !important;

  @media (width < 480px) {
    margin-inline: calc(-1 * var(--form-spacing-inline));
    width: 100vw !important;
    max-width: 100vw;

    .compose-field {
      border-radius: 0;
      outline-offset: -2px;
    }
  }

  &.debug {
    grid-template-columns: 1fr 1fr;
  }

  > .compose-field,
  > .compose-highlight {
    grid-area: 1 / 1 / 2 / 2;
  }

  .compose-highlight {
    user-drag: none;
    user-select: none;
    pointer-events: none;
    touch-action: none;
    padding: 8px;
    color: transparent;
    background-color: transparent;
    border: 2px solid transparent;
    line-height: 1.4;
    overflow: auto;
    unicode-bidi: plaintext;
    -webkit-rtl-ordering: logical;
    rtl-ordering: logical;
    overflow-wrap: break-word;
    white-space: pre-wrap;
    min-height: 5em;
    max-height: 50vh;
    scrollbar-width: none;

    &::-webkit-scrollbar {
      display: none;
    }

    /* Follow textarea styles */
    @media (min-width: 40em) {
      max-height: 65vh;
    }

    mark {
      color: inherit;
    }

    .compose-highlight-url,
    .compose-highlight-hashtag {
      background-color: transparent;
      text-decoration: underline;
      text-decoration-color: var(--link-faded-color);
      text-decoration-thickness: 2px;
      text-underline-offset: 2px;
    }
    .compose-highlight-mention,
    .compose-highlight-emoji-shortcode,
    .compose-highlight-exceeded {
      mix-blend-mode: multiply;
      border-radius: 4px;
      box-shadow: 0 0 0 1px;
    }
    .compose-highlight-mention {
      background-color: var(--orange-light-bg-color);
      box-shadow-color: var(--orange-light-bg-color);
    }
    .compose-highlight-emoji-shortcode {
      background-color: var(--bg-faded-color);
      box-shadow-color: var(--bg-faded-color);
    }
    .compose-highlight-exceeded {
      background-color: var(--red-bg-color);
      box-shadow-color: var(--red-bg-color);
    }

    @media (prefers-color-scheme: dark) {
      .compose-highlight-mention,
      .compose-highlight-emoji-shortcode,
      .compose-highlight-exceeded {
        mix-blend-mode: screen;
      }
    }
  }
}

@keyframes gif-shake {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(5deg);
  }
  50% {
    transform: rotate(0deg);
  }
  75% {
    transform: rotate(-5deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes jump-scare {
  from {
    opacity: 0.5;
    transform: scale(0.25) translateX(80px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateX(0);
  }
}
@keyframes jump-scare-rtl {
  from {
    opacity: 0.5;
    transform: scale(0.25) translateX(-80px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateX(0);
  }
}

.add-button {
  transform-origin: var(--forward) center;
  background-color: var(--bg-blur-color) !important;
  animation: jump-scare 0.2s ease-in-out both;
  :dir(rtl) & {
    animation-name: jump-scare-rtl;
  }

  .icon {
    transition: transform 0.3s ease-in-out;
  }
  &.active {
    .icon {
      transform: rotate(135deg);
    }
  }
}

.gif-picker-button {
  /* span {
    font-weight: bold;
    font-size: 11.5px;
    display: block;
    line-height: 1;
  } */

  &:is(:hover, :focus) {
    .icon {
      animation: gif-shake 0.3s 3;
    }
  }
}

#gif-picker-sheet {
  height: 50vh;

  form {
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;

    input[type='search'] {
      flex-grow: 1;
      min-width: 0;
    }
  }

  main {
    overflow-x: auto;
    overflow-y: hidden;
    mask-image: linear-gradient(
      var(--to-forward),
      transparent 2px,
      black 16px,
      black calc(100% - 16px),
      transparent calc(100% - 2px)
    );

    @media (min-height: 480px) {
      overflow-y: auto;
      max-height: 50vh;
    }

    &.loading {
      opacity: 0.25;
    }

    .ui-state {
      min-height: 100px;
    }

    ul {
      min-height: 100px;
      display: flex;
      gap: 4px;
      list-style: none;
      padding: 8px 2px;
      margin: 0;

      @media (min-height: 480px) {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        grid-auto-rows: 1fr;
      }

      li {
        list-style: none;
        padding: 0;
        margin: 0;
        max-width: 100%;
        display: flex;

        button {
          padding: 4px;
          margin: 0;
          border: none;
          background-color: transparent;
          color: inherit;
          cursor: pointer;
          border-radius: 8px;
          background-color: var(--bg-faded-color);

          @media (min-height: 480px) {
            width: 100%;
            text-align: center;
          }

          &:is(:hover, :focus) {
            background-color: var(--link-bg-color);
            box-shadow: 0 0 0 2px var(--link-light-color);
            filter: none;
          }
        }

        figure {
          margin: 0;
          padding: 0;
          width: var(--figure-width);
          max-width: 100%;

          @media (min-height: 480px) {
            width: 100%;
            text-align: center;
          }

          figcaption {
            font-size: 0.8em;
            padding: 2px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            color: var(--text-insignificant-color);
          }
        }

        img {
          background-color: var(--img-bg-color);
          border-radius: 4px;
          vertical-align: top;
          object-fit: contain;
        }
      }
    }

    .pagination {
      display: flex;
      justify-content: space-between;
      gap: 8px;
      padding: 0;
      margin: 0;
      position: sticky;
      bottom: 0;
      left: 0;
      right: 0;

      @media (min-height: 480px) {
        position: static;
      }
    }
  }
}
