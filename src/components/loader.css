.loader-container {
  display: inline-block;
  width: 16px;
  height: 16px;
  pointer-events: none;
  animation: appear 0.3s ease-in-out 1s both;
  vertical-align: middle;
  margin: 8px;
  vertical-align: baseline !important;
}
.loader-container.abrupt {
  animation: none;
}
.loader-container.hidden {
  visibility: hidden;
}

.loader {
  display: inline-block;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2.5px solid;
  border-color: var(--loader-color) var(--loader-color) transparent transparent;
  animation: loader 1s infinite linear;
}
@keyframes loader {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.loader-container.hidden .loader {
  animation: none;
}
