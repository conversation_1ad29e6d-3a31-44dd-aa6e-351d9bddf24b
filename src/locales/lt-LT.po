msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: lt\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-06-20 01:13\n"
"Last-Translator: \n"
"Language-Team: Lithuanian\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && (n%100>19 || n%100<11) ? 0 : (n%10>=2 && n%10<=9) && (n%100>19 || n%100<11) ? 1 : n%1!=0 ? 2: 3);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: lt\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:137
msgid "Locked"
msgstr "Užrakinta"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:143
msgid "Posts: {0}"
msgstr "Įrašai: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:148
msgid "Last posted: {0}"
msgstr "Paskutinį kartą paskelbta: {0}"

#: src/components/account-block.jsx:163
#: src/components/account-info.jsx:710
msgid "Automated"
msgstr "Automatizuotas"

#: src/components/account-block.jsx:170
#: src/components/account-info.jsx:715
#: src/components/status.jsx:590
msgid "Group"
msgstr "Grupuoti"

#: src/components/account-block.jsx:180
msgid "Mutual"
msgstr "Bendri"

#: src/components/account-block.jsx:184
#: src/components/account-info.jsx:1875
msgid "Requested"
msgstr "Paprašyta"

#: src/components/account-block.jsx:188
#: src/components/account-info.jsx:1866
msgid "Following"
msgstr "Sekama"

#: src/components/account-block.jsx:192
#: src/components/account-info.jsx:1180
msgid "Follows you"
msgstr "Seka jus"

#: src/components/account-block.jsx:200
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# sekėjas} few {# sekėjai} many {# sekėjo} other {# sekėjų}}"

#: src/components/account-block.jsx:209
#: src/components/account-info.jsx:758
msgid "Verified"
msgstr "Patvirtinta"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:224
#: src/components/account-info.jsx:860
msgid "Joined <0>{0}</0>"
msgstr "Prisijungė <0>{0}</0>"

#: src/components/account-info.jsx:63
msgid "Forever"
msgstr "Visam laikui"

#: src/components/account-info.jsx:403
msgid "Unable to load account."
msgstr "Nepavyksta įkelti paskyros."

#: src/components/account-info.jsx:418
msgid "Go to account page"
msgstr "Eiti į paskyros puslapį"

#: src/components/account-info.jsx:447
#: src/components/account-info.jsx:780
#: src/components/account-info.jsx:810
msgid "Followers"
msgstr "Sekėjai"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:451
#: src/components/account-info.jsx:820
#: src/components/account-info.jsx:839
msgid "following.stats"
msgstr "Sekimai"

#: src/components/account-info.jsx:454
#: src/components/account-info.jsx:856
#: src/pages/account-statuses.jsx:487
#: src/pages/search.jsx:344
#: src/pages/search.jsx:491
msgid "Posts"
msgstr "Įrašai"

#: src/components/account-info.jsx:462
#: src/components/account-info.jsx:1236
#: src/components/compose.jsx:2786
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1836
#: src/components/status.jsx:1853
#: src/components/status.jsx:1978
#: src/components/status.jsx:2599
#: src/components/status.jsx:2602
#: src/pages/account-statuses.jsx:531
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1305
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Daugiau"

#: src/components/account-info.jsx:474
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> nurodė, kad jų naujoji paskyra dabar yra:"

#: src/components/account-info.jsx:619
#: src/components/account-info.jsx:1464
msgid "Handle copied"
msgstr "Nukopijuotas socialinis medijos vardas"

#: src/components/account-info.jsx:622
#: src/components/account-info.jsx:1467
msgid "Unable to copy handle"
msgstr "Nepavyksta nukopijuoti socialinės medijos vardo."

#: src/components/account-info.jsx:628
#: src/components/account-info.jsx:1473
msgid "Copy handle"
msgstr "Kopijuoti socialinės medijos vardą"

#: src/components/account-info.jsx:634
msgid "Go to original profile page"
msgstr "Eiti į originalų profilio puslapį"

#: src/components/account-info.jsx:652
msgid "View profile image"
msgstr "Peržiūrėti profilio vaizdą"

#: src/components/account-info.jsx:670
msgid "View profile header"
msgstr "Peržiūrėti profilio antraštę"

#: src/components/account-info.jsx:686
#: src/components/account-info.jsx:1774
#: src/components/account-info.jsx:2300
msgid "Edit profile"
msgstr "Redaguoti profilį"

#: src/components/account-info.jsx:705
msgid "In Memoriam"
msgstr "Atminimui"

#: src/components/account-info.jsx:787
#: src/components/account-info.jsx:830
msgid "This user has chosen to not make this information available."
msgstr "Šis naudotojas pasirinko nepadaryti šią informaciją prieinamą."

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:885
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} originalūs įrašai, {1} atsakymai, {2} pasidalinimai"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:901
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {Paskutinis {0} įrašas per pastarąją {1} dieną} few {Paskutinis 1 įrašas per pastarąsias {2} dienas} many {Paskutinis 1 įrašas per pastarąsias {2} dienos} other {Paskutinis 1 įrašas per pastarąsias {2} dienų}}} few {{3, plural, one {Paskutiniai {4} įrašai per pastarąją 1 dieną} few {Paskutiniai {5} įrašai per pastarąsias {6} dienas} many {Paskutinio {5} įrašo per pastaruosius {6} dienos} other {Paskutinių {5} įrašų per pastarąsias {6} dienų}}} many {{3, plural, one {Paskutiniai {4} įrašai per pastarąją 1 dieną} few {Paskutiniai {5} įrašai per pastarąsias {6} dienas} many {Paskutinio {5} įrašo per pastaruosius {6} dienos} other {Paskutinių {5} įrašų per pastarąsias {6} dienų}}} other {{3, plural, one {Paskutiniai {4} įrašai per pastarąją 1 dieną} few {Paskutiniai {5} įrašai per pastarąsias {6} dienas} many {Paskutinio {5} įrašo per pastaruosius {6} dienos} other {Paskutinių {5} įrašų per pastarąsias {6} dienų}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:917
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {{1} paskutinis įrašas per pastaruosius metus} few {{1} paskutiniai įrašai per pastaruosius metus} many {{1} paskutinio įrašo per pastaruosius metus} other {{1} paskutinių įrašų per pastaruosius metus}}"

#: src/components/account-info.jsx:942
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Originalūs"

#: src/components/account-info.jsx:946
#: src/components/status.jsx:2383
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1024
#: src/pages/status.jsx:1651
msgid "Replies"
msgstr "Atsakymai"

#: src/components/account-info.jsx:950
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Pasidalinimai"

#: src/components/account-info.jsx:956
msgid "Post stats unavailable."
msgstr "Įrašo statistika nepasiekiama."

#: src/components/account-info.jsx:987
msgid "View post stats"
msgstr "Peržiūrėti įrašų statistiką"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1184
msgid "Last post: <0>{0}</0>"
msgstr "Paskutinis įrašas: <0>{0}</0>"

#: src/components/account-info.jsx:1198
msgid "Muted"
msgstr "Nutildyta"

#: src/components/account-info.jsx:1203
msgid "Blocked"
msgstr "Užblokuota"

#: src/components/account-info.jsx:1212
msgid "Private note"
msgstr "Privati pastaba"

#: src/components/account-info.jsx:1269
msgid "Mention <0>@{username}</0>"
msgstr "Paminėti <0>@{username}</0>"

#: src/components/account-info.jsx:1281
msgid "Translate bio"
msgstr "Versti biografiją"

#: src/components/account-info.jsx:1292
msgid "Edit private note"
msgstr "Redaguoti privačią pastabą"

#: src/components/account-info.jsx:1292
msgid "Add private note"
msgstr "Pridėti privačią pastabą"

#: src/components/account-info.jsx:1312
msgid "Notifications enabled for @{username}'s posts."
msgstr "Įjungti pranešimai apie @{username} įrašus."

#: src/components/account-info.jsx:1313
msgid " Notifications disabled for @{username}'s posts."
msgstr " Išjungti pranešimai apie @{username} įrašus."

#: src/components/account-info.jsx:1325
msgid "Disable notifications"
msgstr "Išjungti pranešimus"

#: src/components/account-info.jsx:1326
msgid "Enable notifications"
msgstr "Įjungti pranešimus"

#: src/components/account-info.jsx:1343
msgid "Boosts from @{username} enabled."
msgstr "Įjungti pasidalinimai iš @{username}."

#: src/components/account-info.jsx:1344
msgid "Boosts from @{username} disabled."
msgstr "Išjungti pasidalinimai iš @{username}."

#: src/components/account-info.jsx:1355
msgid "Disable boosts"
msgstr "Išjungti pasidalinimus"

#: src/components/account-info.jsx:1355
msgid "Enable boosts"
msgstr "Įjungti pasidalinimus"

#: src/components/account-info.jsx:1374
msgid "@{username} is no longer featured on your profile."
msgstr "@{username} neberodomas jūsų profilyje."

#: src/components/account-info.jsx:1384
msgid "@{username} is now featured on your profile."
msgstr "@{username} dabar rodomas jūsų profilyje."

#: src/components/account-info.jsx:1392
msgid "Unable to unfeature @{username} on your profile."
msgstr "Nepavyksta neberodyti @{username} jūsų profilyje."

#: src/components/account-info.jsx:1396
msgid "Unable to feature @{username} on your profile."
msgstr "Nepavyksta rodyti @{username} jūsų profilyje."

#: src/components/account-info.jsx:1405
msgid "Don't feature on profile"
msgstr "Nerodyti profilyje"

#: src/components/account-info.jsx:1406
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Rodyti profilyje"

#: src/components/account-info.jsx:1415
msgid "Show featured profiles"
msgstr "Rodyti rodomus profilius"

#: src/components/account-info.jsx:1430
#: src/components/account-info.jsx:1440
#: src/components/account-info.jsx:2046
msgid "Add/Remove from Lists"
msgstr "Pridėti / šalinti iš sąrašų"

#: src/components/account-info.jsx:1490
#: src/components/status.jsx:1258
msgid "Link copied"
msgstr "Nukopijuota nuorada"

#: src/components/account-info.jsx:1493
#: src/components/status.jsx:1261
msgid "Unable to copy link"
msgstr "Nepavyksta atidaryti nuorodos."

#: src/components/account-info.jsx:1499
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1267
#: src/components/status.jsx:3377
msgid "Copy"
msgstr "Kopijuoti"

#: src/components/account-info.jsx:1514
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1283
msgid "Sharing doesn't seem to work."
msgstr "Atrodo, kad bendrinimas neveikia."

#: src/components/account-info.jsx:1520
#: src/components/status.jsx:1289
msgid "Share…"
msgstr "Bendrinti…"

#: src/components/account-info.jsx:1540
msgid "Unmuted @{username}"
msgstr "Atšauktas @{username} nutildymas"

#: src/components/account-info.jsx:1552
msgid "Unmute <0>@{username}</0>"
msgstr "Atšaukti nutildymą <0>@{username}></0>"

#: src/components/account-info.jsx:1568
msgid "Mute <0>@{username}</0>…"
msgstr "Nutildyti <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1600
msgid "Muted @{username} for {0}"
msgstr "Nutildytas @{username} dėl {0}"

#: src/components/account-info.jsx:1612
msgid "Unable to mute @{username}"
msgstr "Nepavyksta nutildyti @{username}."

#: src/components/account-info.jsx:1633
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Pašalinti <0>@{username}</0> iš sekėjų?"

#: src/components/account-info.jsx:1653
msgid "@{username} removed from followers"
msgstr "@{username} pašalintas iš sekėjų"

#: src/components/account-info.jsx:1665
msgid "Remove follower…"
msgstr "Šalinti sekėją…"

#: src/components/account-info.jsx:1676
msgid "Block <0>@{username}</0>?"
msgstr "Bluokuoti <0>@{username}</0>?"

#: src/components/account-info.jsx:1700
msgid "Unblocked @{username}"
msgstr "Atblokuotas @{username}"

#: src/components/account-info.jsx:1708
msgid "Blocked @{username}"
msgstr "Užblokuotas @{username}"

#: src/components/account-info.jsx:1716
msgid "Unable to unblock @{username}"
msgstr "Nepavyksta atblokuoti @{username}."

#: src/components/account-info.jsx:1718
msgid "Unable to block @{username}"
msgstr "Nepavyksta užblokuoti @{username}."

#: src/components/account-info.jsx:1728
msgid "Unblock <0>@{username}</0>"
msgstr "Atblokuoti <0>@{username}</0>"

#: src/components/account-info.jsx:1737
msgid "Block <0>@{username}</0>…"
msgstr "Bluokuoti <0>@{username}</0>…"

#: src/components/account-info.jsx:1754
msgid "Report <0>@{username}</0>…"
msgstr "Pranešti apie <0>@{username}</0>…"

#: src/components/account-info.jsx:1810
msgid "Withdraw follow request?"
msgstr "Atšaukti sekimo prašymą?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1811
msgid "Unfollow @{0}?"
msgstr "Nebesekti @{0}?"

#: src/components/account-info.jsx:1869
msgid "Unfollow…"
msgstr "Nebesekti…"

#: src/components/account-info.jsx:1878
msgid "Withdraw…"
msgstr "Atšaukti…"

#: src/components/account-info.jsx:1885
#: src/components/account-info.jsx:1889
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Sekti"

#: src/components/account-info.jsx:1986
#: src/components/account-info.jsx:2041
#: src/components/account-info.jsx:2175
#: src/components/account-info.jsx:2295
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:891
#: src/components/compose.jsx:2742
#: src/components/compose.jsx:3222
#: src/components/compose.jsx:3431
#: src/components/compose.jsx:3661
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:75
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3101
#: src/components/status.jsx:3341
#: src/components/status.jsx:3850
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1392
msgid "Close"
msgstr "Uždaryti"

#: src/components/account-info.jsx:1991
msgid "Translated Bio"
msgstr "Išversta biografija"

#: src/components/account-info.jsx:2086
msgid "Unable to remove from list."
msgstr "Nepavyksta pašalinti iš sąrašo."

#: src/components/account-info.jsx:2087
msgid "Unable to add to list."
msgstr "Nepavyksta pridėti prie sąrašo."

#: src/components/account-info.jsx:2106
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Nepavyksta įkelti sąrašų."

#: src/components/account-info.jsx:2110
msgid "No lists."
msgstr "Nėra sąrašų."

#: src/components/account-info.jsx:2121
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Naujas sąrašas"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2180
msgid "Private note about <0>@{0}</0>"
msgstr "Privati pastaba apie <0>@{0}</0>"

#: src/components/account-info.jsx:2210
msgid "Unable to update private note."
msgstr "Nepavyksta atnaujinti privačios pastabos."

#: src/components/account-info.jsx:2233
#: src/components/account-info.jsx:2531
msgid "Cancel"
msgstr "Atšaukti"

#: src/components/account-info.jsx:2238
msgid "Save & close"
msgstr "Išsaugoti ir uždaryti"

#: src/components/account-info.jsx:2355
msgid "Unable to update profile."
msgstr "Nepavyksta atnaujinti profilio."

#: src/components/account-info.jsx:2362
msgid "Header picture"
msgstr "Antraštinė nuotrauka"

#: src/components/account-info.jsx:2414
msgid "Profile picture"
msgstr "Profilio nuotrauka"

#: src/components/account-info.jsx:2466
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Pavadinimas"

#: src/components/account-info.jsx:2479
msgid "Bio"
msgstr "Biografija"

#: src/components/account-info.jsx:2492
msgid "Extra fields"
msgstr "Papildomi laukai"

#: src/components/account-info.jsx:2498
msgid "Label"
msgstr "Etiketė"

#: src/components/account-info.jsx:2501
msgid "Content"
msgstr "Turinys"

#: src/components/account-info.jsx:2534
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Išsaugoti"

#: src/components/account-info.jsx:2588
msgid "username"
msgstr "naudotojo vardas"

#: src/components/account-info.jsx:2592
msgid "server domain name"
msgstr "serverio domeno vardas"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2658
msgid "Profiles featured by @{0}"
msgstr "Profilius, kuriuos rodė @{0}"

#: src/components/account-info.jsx:2684
msgid "No featured profiles."
msgstr "Nėra rodomų profilių."

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Išjungtas slėpimo režimas"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Įjungtas slėpimo režimas"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Pagrindinis"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Sukurti"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Suplanuoti įrašai"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Pridėti prie gijos"

#: src/components/compose.jsx:211
msgid "Take photo or video"
msgstr "Daryti nuotrauką arba vaizdo įrašą"

#: src/components/compose.jsx:212
msgid "Add media"
msgstr "Pridėti mediją"

#: src/components/compose.jsx:213
msgid "Add custom emoji"
msgstr "Pridėti pasirinktinį jaustuką"

#: src/components/compose.jsx:214
msgid "Add GIF"
msgstr "Pridėti GIF"

#: src/components/compose.jsx:215
msgid "Add poll"
msgstr "Pridėti apklausą"

#: src/components/compose.jsx:216
msgid "Schedule post"
msgstr "Planuoti įrašą"

#: src/components/compose.jsx:416
msgid "You have unsaved changes. Discard this post?"
msgstr "Turite neišsaugotų pakeitimų. Atmesti šią įrašą?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:654
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {{1} failas nepalaikomas.} few {{2} failai nepalaikomi.} many {{2} failo nepalaikoma.} other {{2} failų nepalaikoma.}}"

#: src/components/compose.jsx:664
#: src/components/compose.jsx:682
#: src/components/compose.jsx:1792
#: src/components/compose.jsx:1917
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Galite pridėti tik iki # failo.} few {Galite pridėti tik iki # failų.} many {Galite pridėti tik iki # failo.} other {Galite pridėti tik iki # failų.}}"

#: src/components/compose.jsx:872
msgid "Pop out"
msgstr "Išskleisti"

#: src/components/compose.jsx:879
msgid "Minimize"
msgstr "Sumažinti"

#: src/components/compose.jsx:915
msgid "Looks like you closed the parent window."
msgstr "Atrodo, kad uždarėte pirminį langą."

#: src/components/compose.jsx:922
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Atrodo, kad pirminiame lange jau atidarytas sukūrimo laukas ir šiuo metu skelbiamas. Palauk, kol jis bus baigtas, ir pabandyk dar kartą vėliau."

#: src/components/compose.jsx:927
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Atrodo, kad pirminiame lange jau yra atidarytas sukūrimo laukas. Suskleisčius šiame lange, bus atmesti pakeitimai, kuriuos padarėte pirminiame lange. Tęsti?"

#: src/components/compose.jsx:970
msgid "Pop in"
msgstr "Suskleisti"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:980
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Atsakant į @{0} įrašą (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:990
msgid "Replying to @{0}’s post"
msgstr "Atsakant į @{0} įrašą"

#: src/components/compose.jsx:1003
msgid "Editing source post"
msgstr "Redaguojamas šaltinio įrašas"

#: src/components/compose.jsx:1056
msgid "Poll must have at least 2 options"
msgstr "Apklausa turi turėti bent 2 parinktis."

#: src/components/compose.jsx:1060
msgid "Some poll choices are empty"
msgstr "Kai kurie apklausos pasirinkimai yra tušti."

#: src/components/compose.jsx:1073
msgid "Some media have no descriptions. Continue?"
msgstr "Kai kurios medijos neturi aprašymų. Tęsti?"

#: src/components/compose.jsx:1125
msgid "Attachment #{i} failed"
msgstr "Nepavyko #{i} priedas."

#: src/components/compose.jsx:1221
#: src/components/status.jsx:2166
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Turinio įspėjimas"

#: src/components/compose.jsx:1237
msgid "Content warning or sensitive media"
msgstr "Turinio įspėjimas arba jautri medija"

#: src/components/compose.jsx:1273
#: src/components/status.jsx:96
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Vieša"

#: src/components/compose.jsx:1278
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:97
msgid "Local"
msgstr "Vietinis"

#: src/components/compose.jsx:1282
#: src/components/status.jsx:98
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Neįtrauktas į sąrašą"

#: src/components/compose.jsx:1285
#: src/components/status.jsx:99
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Tik sekėjai"

#: src/components/compose.jsx:1288
#: src/components/status.jsx:100
#: src/components/status.jsx:2042
msgid "Private mention"
msgstr "Privatus paminėjimas"

#: src/components/compose.jsx:1297
msgid "Post your reply"
msgstr "Skelbti savo atsakymą"

#: src/components/compose.jsx:1299
msgid "Edit your post"
msgstr "Redaguoti savo įrašą"

#: src/components/compose.jsx:1300
msgid "What are you doing?"
msgstr "Ką tu darai?"

#: src/components/compose.jsx:1379
msgid "Mark media as sensitive"
msgstr "Žymėti mediją kaip jautrią"

#: src/components/compose.jsx:1416
msgid "Posting on <0/>"
msgstr "Skelbimiama <0/>"

#: src/components/compose.jsx:1447
#: src/components/compose.jsx:3280
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Pridėti"

#: src/components/compose.jsx:1673
msgid "Schedule"
msgstr "Planuoti"

#: src/components/compose.jsx:1675
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1030
#: src/components/status.jsx:1816
#: src/components/status.jsx:1817
#: src/components/status.jsx:2503
msgid "Reply"
msgstr "Atsakyti"

#: src/components/compose.jsx:1677
msgid "Update"
msgstr "Atnaujinti"

#: src/components/compose.jsx:1678
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Skelbti"

#: src/components/compose.jsx:1804
msgid "Downloading GIF…"
msgstr "Atsisiunčiama GIF…"

#: src/components/compose.jsx:1832
msgid "Failed to download GIF"
msgstr "Nepavyko atsisiųsti GIF."

#: src/components/compose.jsx:2047
#: src/components/compose.jsx:2124
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Daugiau…"

#: src/components/compose.jsx:2556
msgid "Uploaded"
msgstr "Įkelta"

#: src/components/compose.jsx:2569
msgid "Image description"
msgstr "Vaizdo aprašymas"

#: src/components/compose.jsx:2570
msgid "Video description"
msgstr "Vaizdo įrašo aprašymas"

#: src/components/compose.jsx:2571
msgid "Audio description"
msgstr "Garso įrašo aprašymas"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2606
#: src/components/compose.jsx:2626
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Per didelis failo dydis. Įkeliant gali kilti problemų. Bandyk sumažinti failo dydį nuo {0} iki {1} arba mažiau."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2618
#: src/components/compose.jsx:2638
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "Per didelis matmuo. Įkeliant gali kilti problemų. Bandykite sumažinti matmenis nuo {0}×{1} tšk. iki {2}×{3} tšk."

#: src/components/compose.jsx:2646
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Per didelis kadrų dažnis. Įkeliant gali kilti problemų."

#: src/components/compose.jsx:2706
#: src/components/compose.jsx:2956
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Šalinti"

#: src/components/compose.jsx:2723
#: src/compose.jsx:84
msgid "Error"
msgstr "Klaida"

#: src/components/compose.jsx:2748
msgid "Edit image description"
msgstr "Redaguoti vaizdo aprašymą"

#: src/components/compose.jsx:2749
msgid "Edit video description"
msgstr "Redaguoti vaizdo įrašo aprašymą"

#: src/components/compose.jsx:2750
msgid "Edit audio description"
msgstr "Redaguoti garso įrašo aprašymą"

#: src/components/compose.jsx:2795
#: src/components/compose.jsx:2844
msgid "Generating description. Please wait…"
msgstr "Generuojamas aprašymas. Palauk…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2815
msgid "Failed to generate description: {0}"
msgstr "Nepavyko sugeneruoti aprašo: {0}."

#: src/components/compose.jsx:2816
msgid "Failed to generate description"
msgstr "Nepavyko sugeneruoti aprašymo."

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2834
#: src/components/compose.jsx:2880
msgid "Generate description…"
msgstr "Generuoti aprašymą…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2867
msgid "Failed to generate description{0}"
msgstr "Nepavyko sugeneruoti aprašo{0}."

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2882
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>– eksperimentinė</0>"

#: src/components/compose.jsx:2901
msgid "Done"
msgstr "Atlikta"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2937
msgid "Choice {0}"
msgstr "{0} pasirinkimas"

#: src/components/compose.jsx:2984
msgid "Multiple choices"
msgstr "Keli pasirinkimai"

#: src/components/compose.jsx:2987
msgid "Duration"
msgstr "Trukmė"

#: src/components/compose.jsx:3018
msgid "Remove poll"
msgstr "Šalinti apklausą"

#: src/components/compose.jsx:3239
msgid "Search accounts"
msgstr "Ieškoti paskyrų"

#: src/components/compose.jsx:3293
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Klaida įkeliant paskyras."

#: src/components/compose.jsx:3437
msgid "Custom emojis"
msgstr "Pasirinktiniai jaustukai"

#: src/components/compose.jsx:3457
msgid "Search emoji"
msgstr "Ieškoti jaustukų"

#: src/components/compose.jsx:3488
msgid "Error loading custom emojis"
msgstr "Klaida įkeliant pasirinktinius jaustukus."

#: src/components/compose.jsx:3499
msgid "Recently used"
msgstr "Neseniai naudoti"

#: src/components/compose.jsx:3500
msgid "Others"
msgstr "Kiti"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3538
msgid "{0} more…"
msgstr "{0} daugiau…"

#: src/components/compose.jsx:3676
msgid "Search GIFs"
msgstr "Ieškoti GIF"

#: src/components/compose.jsx:3691
msgid "Powered by GIPHY"
msgstr "Veikiama su „GIPHY“"

#: src/components/compose.jsx:3699
msgid "Type to search GIFs"
msgstr "Rašyk, kad ieškotum GIF"

#: src/components/compose.jsx:3797
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Ankstesnis"

#: src/components/compose.jsx:3815
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Kitas"

#: src/components/compose.jsx:3832
msgid "Error loading GIFs"
msgstr "Klaida įkeliant GIF."

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Neišsiųsti juodraščiai"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Atrodo, kad turi neišsiųstų juodraščių. Tęskime nuo ten, kur baigei."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Ištrinti šį juodraštį?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Klaida ištrinant juodraštį. Bandyk dar kartą."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1433
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Ištrinti…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Klaida gaunant atsakymo į būseną."

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Ištrinti visus juodraščius?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Klaida ištrinant juodraščius. Bandyk dar kartą."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Ištrinti visus…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Juodraščių nerasta."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Apklausa"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:367
msgid "Media"
msgstr "Medija"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Atidaryti naujame lange"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Priimti"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Atmesti"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Priimta"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Atmesta"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:334
#: src/pages/search.jsx:367
msgid "Accounts"
msgstr "Paskyros"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:561
#: src/pages/status.jsx:1425
msgid "Show more…"
msgstr "Rodyti daugiau…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:566
msgid "The end."
msgstr "Pabaiga."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Nėra ką rodyti."

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Spartieji klaviatūros klavišai"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Spartieji klaviatūros klavišos žinynas"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Sekantis įrašas"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Ankstesnis įrašas"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Praleisti karuselę į kitą įrašą"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Lyg2 (Shift)</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Praleisti karuselę į ankstesnį įrašą"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Lyg2 (Shift)</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Įkelti naujų įrašų"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Atidaryti įrašo informaciją"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Įvesti (Enter)</0> arba <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Išskleisti turinio įspėjimą arba<0/>perjungti išskleistą / suskleistą giją"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Uždaryti įrašą arba dialogo langus"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Gr (Esc)</0> arba <1>Naikinimo klavišas (Backspace)</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Fokusuoti stulpelį daugiastulpelių režime"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> iki <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Fokusuoti sekantį stulpelį daugiastulpelių režime"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Fokusuoti ankstesnį stulpelį daugiastulpelių režime"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Sukurti naują įrašą"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Sukurti naują įrašą (naujas langas)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Lyg2 (Shift)</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Siųsti įrašą"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Vald (Ctrl)</0> + <1>Įvesti (Enter)</1> arba <2>⌘</2> + <3>Įvesti (Enter)</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:73
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:46
#: src/pages/search.jsx:316
msgid "Search"
msgstr "Paieška"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Atsakyti (naujas langas)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Lyg2 (Shift)</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Patinka (pamėgti)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> arba <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1038
#: src/components/status.jsx:2530
#: src/components/status.jsx:2553
#: src/components/status.jsx:2554
msgid "Boost"
msgstr "Pasidalinti"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Lyg2 (Shift)</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1101
#: src/components/status.jsx:2578
#: src/components/status.jsx:2579
msgid "Bookmark"
msgstr "Prid. į žym."

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Perjungti slėpimo režimą"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Lyg2 (Shift)</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Redaguoti sąrašą"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Nepavyksta redaguoti sąrašo."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Nepavyksta sukurti sąrašo."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Rodyti atsakymus sąrašo nariams"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Rodyti atsakymus žmonėms, kuriuos seku"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Nerodyti atsakymų"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Paslėpti šio sąrašo pranešimus iš pagrindinio / sekimo"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Kurti"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Ištrinti šį sąrašą?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Nepavyksta ištrinti sąrašo."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "Įrašai šiame sąraše paslėpti nuo pagrindinio / sekimų"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Medijos aprašymas"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1144
#: src/components/status.jsx:1153
#: src/components/translation-block.jsx:237
msgid "Translate"
msgstr "Versti"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1172
msgid "Speak"
msgstr "Kalbėti"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Atidaryti originalią mediją naujame lange"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Atidaryti originalią mediją"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Bandymas apibūdinti vaizdą. Palauk…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Nepavyko apibūdinti vaizdo."

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Apibūdinti vaizdą…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Peržiūrėti įrašą"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Jautri medija"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Filtruota: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3680
#: src/components/status.jsx:3776
#: src/components/status.jsx:3854
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Filtruota"

#: src/components/media.jsx:469
msgid "Open file"
msgstr "Atverti failą"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Įrašas suplanuotas"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Įrašas paskelbtas. Peržiūrėk."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Atsakymas suplanuotas"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Atsakymas paskelbtas. Peržiūrėk."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Įrašas atnaujintas. Peržiūrėk."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Meniu"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Perkrauti puslapį dabar, kad atnaujinti?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Yra naujas naujinimas…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "Sekimai"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Pasivijimas"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Paminėjimai"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Pranešimai"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Naujas"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Profilis"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Žymės"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Patiktukų"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Sekami saitažodžiai"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:333
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Filtrai"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Nutildyti naudotojai"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Nutildyti naudotojai…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Užblokuoti naudotojai"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Užblokuoti naudotojai…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Paskyros…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:195
#: src/pages/status.jsx:924
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Prisijungti"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Tendencinga"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Federacinis"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Trumposios nuorodos / stulpeliai…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Nustatymai…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Sąrašai"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Visi sąrašai"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Pranešimas"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Šis pranešimas yra iš kitos tavo paskyros."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Peržiūrėti visus pranešimus"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} sureagavo į tavo įrašą su {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} paskelbė įrašą."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr ""

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, one {<0><1>{0}</1> žmogus seka jus.} few {<0><1>{0}</1> žmonės</0> seka jus.} many {<0><1>{0}</1> žmones</0> seka jus.}=1 {{account}} other {<0><1>{0}</1> žmonių</0> seka jus.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} paprašė tave sekti."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr ""

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Baigėsi apklausa, kurioje balsavai arba kurią sukūrei."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Baigėsi apklausa, kurią sukūrei."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Baigėsi apklausa, kurioje balsavote."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Įrašas, su kuriuo bendravote, buvo redaguotas."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr ""

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} užsiregistravo."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} pranešė apie {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Prarasti sąryšiai su <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Prižiūrėjimo įspėjimas"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Jūsų #Wrapstodon {year} jau čia!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Administratorius (-ė) iš <0>{from}</0> pristabdė <1>{targetName}</1>, o tai reiškia, kad nebegali gauti iš jų naujienų ir su jais bendrauti."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Administratorius iš <0>{from}</0> užblokavo <1>{targetName}</1>. Paveikti sekėjai: {followersCount}, sekimai: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Užblokavote <0>{targetName}</0>. Pašalinti sekėjai: {followersCount}, sekimai: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Tavo paskyra gavo prižiūrėjimo įspėjimą."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Tavo paskyra buvo išjungta."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Kai kurie tavo įrašai buvo pažymėtos kaip jautrios."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Kai kurios tavo įrašai buvo ištrinti."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "Nuo šiol tavo įrašai bus pažymėti kaip jautrūs."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Tavo paskyra buvo apribota."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Tavo paskyra buvo pristabdyta."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Nežinomas pranešimo tipas: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1115
#: src/components/status.jsx:1125
msgid "Boosted/Liked by…"
msgstr "Pasidalino / patiko…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Patiko…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Pasidalino…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Seka…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Sužinoti daugiau <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Peržiūrėti #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:328
msgid "Read more →"
msgstr "Skaityti daugiau →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Balsuota"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# balsas} few {# balsai} many {# balso} other {# balsų}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Slėpti rezultatus"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Balsuoti"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1294
#: src/pages/status.jsx:1317
msgid "Refresh"
msgstr "Atnaujinti"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Rodyti rezultatus"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> balsas} few {<1>{1}</1> balsai} many {<1>{1}</1> balso} other {<1>{1}</1> balsų}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> balsuotojas} few {<1>{1}</1> balsuotojai} many {<1>{1}</1> balsuotojo} other {<1>{1}</1> balsuotojų}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Baigėsi <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Baigėsi"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Baigsis <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Baigsis"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0} sek."

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0} min."

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0} val."

#: src/components/report-modal.jsx:29
msgid "Spam"
msgstr "Brukalas"

#: src/components/report-modal.jsx:30
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Kenkėjiškos nuorodos, netikras įsitraukimas arba pasikartojantys atsakymai"

#: src/components/report-modal.jsx:33
msgid "Illegal"
msgstr "Neteisėta"

#: src/components/report-modal.jsx:34
msgid "Violates the law of your or the server's country"
msgstr "Pažeidžia tavo arba serverio šalies įstatymus"

#: src/components/report-modal.jsx:37
msgid "Server rule violation"
msgstr "Serverio taisyklių pažeidimas"

#: src/components/report-modal.jsx:38
msgid "Breaks specific server rules"
msgstr "Pažeidžia konkrečias serverio taisykles"

#: src/components/report-modal.jsx:39
msgid "Violation"
msgstr "Pažeidimas"

#: src/components/report-modal.jsx:42
msgid "Other"
msgstr "Kita"

#: src/components/report-modal.jsx:43
msgid "Issue doesn't fit other categories"
msgstr "Problema netinka kitoms kategorijoms"

#: src/components/report-modal.jsx:68
msgid "Report Post"
msgstr "Pranešti apie įrašą"

#: src/components/report-modal.jsx:68
msgid "Report @{username}"
msgstr "Pranešti apie @{username}"

#: src/components/report-modal.jsx:104
msgid "Pending review"
msgstr "Laukiama apžvalgos"

#: src/components/report-modal.jsx:146
msgid "Post reported"
msgstr "Įrašas praneštas"

#: src/components/report-modal.jsx:146
msgid "Profile reported"
msgstr "Profilis praneštas"

#: src/components/report-modal.jsx:154
msgid "Unable to report post"
msgstr "Nepavyksta pranešti apie įrašą."

#: src/components/report-modal.jsx:155
msgid "Unable to report profile"
msgstr "Nepavyksta pranešti apie profilį"

#: src/components/report-modal.jsx:163
msgid "What's the issue with this post?"
msgstr "Kokia problema su šiuo įrašu?"

#: src/components/report-modal.jsx:164
msgid "What's the issue with this profile?"
msgstr "Kokia problema su šiuo profiliu?"

#: src/components/report-modal.jsx:233
msgid "Additional info"
msgstr "Papildoma informacija"

#: src/components/report-modal.jsx:256
msgid "Forward to <0>{domain}</0>"
msgstr "Persiųsti į <0>{domain}</0>"

#: src/components/report-modal.jsx:266
msgid "Send Report"
msgstr "Siųsti ataskaitą"

#: src/components/report-modal.jsx:275
msgid "Muted {username}"
msgstr "Nutildytas {username}"

#: src/components/report-modal.jsx:278
msgid "Unable to mute {username}"
msgstr "Nepavyksta nutildyti {username}."

#: src/components/report-modal.jsx:283
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Siųsti ataskaitą <0>+ nutildyti profilį</0>"

#: src/components/report-modal.jsx:294
msgid "Blocked {username}"
msgstr "Užblokuotas {username}"

#: src/components/report-modal.jsx:297
msgid "Unable to block {username}"
msgstr "Nepavyksta užblokuoti {username}."

#: src/components/report-modal.jsx:302
msgid "Send Report <0>+ Block profile</0>"
msgstr "Siųsti ataskaitą <0>+ blokuoti profilį</0>"

#: src/components/search-form.jsx:203
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ paskyros, saitažodžiai ir įrašai</0>"

#: src/components/search-form.jsx:216
msgid "Posts with <0>{query}</0>"
msgstr "Įrašai su <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:228
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Įrašai pažymėti su <0>#{0}</0>"

#: src/components/search-form.jsx:242
msgid "Look up <0>{query}</0>"
msgstr "Ieškoti <0>{query}</0>"

#: src/components/search-form.jsx:253
msgid "Accounts with <0>{query}</0>"
msgstr "Paskyros su <0>{query}</0>"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Pagrindinis / sekimai"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Viešieji (vietiniai / federaciniai)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Paskyra"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Saitažodis"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "Sąrašo ID"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Tik vietinė"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:199
msgid "Instance"
msgstr "Serveris"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Pasirinktinis, pvz., mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Paieškos terminas"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Pasirinktinis, nebent naudojamas daugiastulpelių režimas"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "pvz., „PixelArt“ (ne daugiau kaip 5, atskirti tarpais)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Tik medija"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Trumposios nuorodos"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "beta versija"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Nurodyk trumpųjų nuorodų sąrašą, kuris bus rodomas kaip:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Slankusis mygtukas"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Skirtukas / meniu juosta"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Daugiastulpelių"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Nepasiekiama dabartiniame rodinio režime."

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Perkelti aukštyn"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Perkelti žemyn"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1395
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Redaguoti"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Pridėk daugiau nei vieną trumpąją nuorodą / stulpelį, kad tai veiktų."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Kol kas nėra stulpelių. Paliesk mygtuką Pridėti stulpelį."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Kol kas nėra trumpųjų nuorodų. Paliesk mygtuką Pridėti trumpąją nuorodą."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Nežinai, ką pridėti?<0/>Pirmiausia pabandyk pridėti <1>Pagrindinis / sekimai ir pranešimai</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Didžiausias {SHORTCUTS_LIMIT} stulpelių skaičius"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Didžiausias {SHORTCUTS_LIMIT} trumpųjų nuorodų skaičius"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Importuoti / eksportuoti"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Pridėti stulpelį…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Pridėti trumpąją nuorodą…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Konkretus sąrašas neprivalomas. Daugiastulpelių režime sąrašas yra privalomas, kitaip stulpelis nebus rodomas."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Daugiastulpelių režime privaloma nurodyti paieškos terminą, kitaip stulpelis nebus rodomas."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Palaikomi keli saitažodžiai. Atskirti tarpais."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Redaguoti trumpąją nuorodą"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Pridėti trumpąją nuorodą"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Laiko skalė"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Sąrašas"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Importuoti / eksportuoti <0>trumposios nuorodos</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Importuoti"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Įklijuok trumpąsias nuorodas čia"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Atsisiunčiamos išsaugotos trumposios nuorodos iš serverio…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Nepavyksta atsisiųsti trumpųjų nuorodų."

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Atsisiųsti trumpųjų nuorodų iš serverio"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Egzistuoja dabartiniuose trumpuosiuose nuorodose"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "Sąrašas gali neveikti, jei jis yra iš kitos paskyros."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Netinkamas nustatymų formatas"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Pridėti prie esamų trumpųjų nuorodų?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Bus pridėti tik tie trumpieji nuorodos, kurių neegzistuoja dabartinėse trumposiose nuorodose."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Nėra naujų trumpųjų nuorodų importuoti"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Importuotos trumposios nuorodos. Viršytas didžiausias {SHORTCUTS_LIMIT} ribą, todėl likusieji neimportuojami."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Importuotos trumposios nuorodos"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Importuoti ir pridėti…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Perrašyti esamas trumpąsias nuorodas?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Importuoti trumpąsias nuorodas?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "arba perrašyti…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Importuoti…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Eksportuoti"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Nukopijuotos trumposios nuorodos"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Nepavyksta nukopijuoti trumpųjų nuorodų."

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Nukopijuoti trumpųjų nuorodų nustatymai"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Nepavyksta nukopijuoti trumpųjų nuorodų nustatymų."

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Bendrinti"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Išsaugomos trumpųjų nuorodų į serverį…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Išsaugotos trumposios nuorodos"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Nepavyksta išsaugoti trumpųjų nuorodų."

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Sinchronizuoti su serveriu"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# simbolis} few {# simboliai} many {# simbolio} other {# simbolių}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Neapdorotos trumposios nuorodos JSON"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Importuoti / eksportuoti nustatymus iš / į serverį (labai eksperimentinis)"

#: src/components/status.jsx:614
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>pasidalino</1>"

#: src/components/status.jsx:713
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Atsiprašome, tavo dabartinis prisijungtas serveris negali sąveikauti su šiuo įrašu iš kito serverio."

#. placeholder {0}: username || acct
#: src/components/status.jsx:867
msgid "Unliked @{0}'s post"
msgstr "Panaikintas @{0} patiktuko įrašas"

#. placeholder {0}: username || acct
#: src/components/status.jsx:868
msgid "Liked @{0}'s post"
msgstr "Patiko @{0} įrašas"

#. placeholder {0}: username || acct
#: src/components/status.jsx:907
msgid "Unbookmarked @{0}'s post"
msgstr "Pašalintas @{0} įrašas iš žymių"

#. placeholder {0}: username || acct
#: src/components/status.jsx:908
msgid "Bookmarked @{0}'s post"
msgstr "Pridėtas @{0} įrašas prie žymių"

#: src/components/status.jsx:1007
msgid "Some media have no descriptions."
msgstr "Kai kurios medijos neturi aprašymų."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1014
msgid "Old post (<0>{0}</0>)"
msgstr "Senasis įrašas (<0>{0}</0>)"

#: src/components/status.jsx:1038
#: src/components/status.jsx:1078
#: src/components/status.jsx:2530
#: src/components/status.jsx:2553
msgid "Unboost"
msgstr "Nebepasidalinti"

#: src/components/status.jsx:1054
#: src/components/status.jsx:2545
msgid "Quote"
msgstr "Cituoti"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1066
#: src/components/status.jsx:1532
msgid "Unboosted @{0}'s post"
msgstr "Panaikintas @{0} įrašo pasidalinimas"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1067
#: src/components/status.jsx:1533
msgid "Boosted @{0}'s post"
msgstr "Pasidalintas @{0} įrašas"

#: src/components/status.jsx:1079
msgid "Boost…"
msgstr "Pasidalinti…"

#: src/components/status.jsx:1091
#: src/components/status.jsx:1826
#: src/components/status.jsx:2566
msgid "Unlike"
msgstr "Nebepatinka"

#: src/components/status.jsx:1092
#: src/components/status.jsx:1826
#: src/components/status.jsx:1827
#: src/components/status.jsx:2566
#: src/components/status.jsx:2567
msgid "Like"
msgstr "Patinka"

#: src/components/status.jsx:1101
#: src/components/status.jsx:2578
msgid "Unbookmark"
msgstr "Pašalinti iš žymių"

#: src/components/status.jsx:1184
msgid "Post text copied"
msgstr "Įrašo tekstas nukopijuotas"

#: src/components/status.jsx:1187
msgid "Unable to copy post text"
msgstr "Nepavyksta nukopijuoti įrašo teksto."

#: src/components/status.jsx:1193
msgid "Copy post text"
msgstr "Kopijuoti įrašo tekstą"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1211
msgid "View post by <0>@{0}</0>"
msgstr "Peržiūrėti įrašą, kurį sukūrė <0>@{0}</0>"

#: src/components/status.jsx:1232
msgid "Show Edit History"
msgstr "Rodyti redagavimo istoriją"

#: src/components/status.jsx:1235
msgid "Edited: {editedDateText}"
msgstr "Redaguota: {editedDateText}"

#: src/components/status.jsx:1302
#: src/components/status.jsx:3346
msgid "Embed post"
msgstr "Įterptas įrašas"

#: src/components/status.jsx:1316
msgid "Conversation unmuted"
msgstr "Atšauktas pokalbio nutildymas"

#: src/components/status.jsx:1316
msgid "Conversation muted"
msgstr "Pokalbis nutildytas"

#: src/components/status.jsx:1322
msgid "Unable to unmute conversation"
msgstr "Nepavyksta atšaukti pokalbio nutildymą."

#: src/components/status.jsx:1323
msgid "Unable to mute conversation"
msgstr "Nepavyksta nutildyti pokalbio."

#: src/components/status.jsx:1332
msgid "Unmute conversation"
msgstr "Atšaukti pokalbio nutildymą"

#: src/components/status.jsx:1339
msgid "Mute conversation"
msgstr "Nutildyti pokalbį"

#: src/components/status.jsx:1355
msgid "Post unpinned from profile"
msgstr "Įrašas atsegtas iš profilio"

#: src/components/status.jsx:1356
msgid "Post pinned to profile"
msgstr "Įrašas prisegtas prie profilio"

#: src/components/status.jsx:1361
msgid "Unable to unpin post"
msgstr "Nepavyksta atsegti įrašo."

#: src/components/status.jsx:1361
msgid "Unable to pin post"
msgstr "Nepavyksta atsegti įrašo."

#: src/components/status.jsx:1370
msgid "Unpin from profile"
msgstr "Atsegti iš profilio"

#: src/components/status.jsx:1377
msgid "Pin to profile"
msgstr "Prisegti prie profilio"

#: src/components/status.jsx:1406
msgid "Delete this post?"
msgstr "Ištrinti šį įrašą?"

#: src/components/status.jsx:1422
msgid "Post deleted"
msgstr "Įrašas ištrintas"

#: src/components/status.jsx:1425
msgid "Unable to delete post"
msgstr "Nepavyksta ištrinti įrašo."

#: src/components/status.jsx:1453
msgid "Report post…"
msgstr "Pranešti apie įrašą…"

#: src/components/status.jsx:1827
#: src/components/status.jsx:1863
#: src/components/status.jsx:2567
msgid "Liked"
msgstr "Patinka"

#: src/components/status.jsx:1860
#: src/components/status.jsx:2554
msgid "Boosted"
msgstr "Pasidalinta"

#: src/components/status.jsx:1870
#: src/components/status.jsx:2579
msgid "Bookmarked"
msgstr "Pridėta"

#: src/components/status.jsx:1874
msgid "Pinned"
msgstr "Prisegta"

#: src/components/status.jsx:1920
#: src/components/status.jsx:2391
msgid "Deleted"
msgstr "Ištrinta"

#: src/components/status.jsx:1961
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# atsakymas} few {# atsakymai} many {# atsakymo} other {# atsakymų}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2051
msgid "Thread{0}"
msgstr "Gija{0}"

#: src/components/status.jsx:2129
#: src/components/status.jsx:2191
#: src/components/status.jsx:2287
msgid "Show less"
msgstr "Rodyti mažiau"

#: src/components/status.jsx:2129
#: src/components/status.jsx:2191
msgid "Show content"
msgstr "Rodyti turinį"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2283
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Filtruota: {0}"

#: src/components/status.jsx:2287
msgid "Show media"
msgstr "Rodyti mediją"

#: src/components/status.jsx:2427
msgid "Edited"
msgstr "Redaguota"

#: src/components/status.jsx:2504
msgid "Comments"
msgstr "Komentarai"

#. More from [Author]
#: src/components/status.jsx:2804
msgid "More from <0/>"
msgstr "Daugiau iš <0/>"

#: src/components/status.jsx:3106
msgid "Edit History"
msgstr "Redagavimo istoriją"

#: src/components/status.jsx:3110
msgid "Failed to load history"
msgstr "Nepavyko įkelti istorijos."

#: src/components/status.jsx:3115
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Įkeliama…"

#: src/components/status.jsx:3351
msgid "HTML Code"
msgstr "HTML kodas"

#: src/components/status.jsx:3368
msgid "HTML code copied"
msgstr "Nukopijuotas HTML kodas"

#: src/components/status.jsx:3371
msgid "Unable to copy HTML code"
msgstr "Nepavyksta nukopijuoti HTML kodo."

#: src/components/status.jsx:3383
msgid "Media attachments:"
msgstr "Medijos priedai:"

#: src/components/status.jsx:3405
msgid "Account Emojis:"
msgstr "Paskyros jaustukai:"

#: src/components/status.jsx:3436
#: src/components/status.jsx:3481
msgid "static URL"
msgstr "statinis URL"

#: src/components/status.jsx:3450
msgid "Emojis:"
msgstr "Jaustukai:"

#: src/components/status.jsx:3495
msgid "Notes:"
msgstr "Pastabos:"

#: src/components/status.jsx:3499
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Tai – statiškas, nestilingas ir be skriptis. Gali reikėti taikyti savo stilius ir redaguoti pagal poreikį."

#: src/components/status.jsx:3505
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Apklausos nėra interaktyvios, tampa sąrašu su balsų skaičiais."

#: src/components/status.jsx:3510
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Medijos priedai gali būti vaizdai, vaizdo įrašai, garso įrašai arba bet kokio tipo failai."

#: src/components/status.jsx:3516
msgid "Post could be edited or deleted later."
msgstr "Įrašas gali būti redaguojamas arba ištrintas vėliau."

#: src/components/status.jsx:3522
msgid "Preview"
msgstr "Peržiūrėti"

#: src/components/status.jsx:3531
msgid "Note: This preview is lightly styled."
msgstr "Pastaba: ši peržiūra yra šiek tiek stilizuota."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3784
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> pasidalino"

#: src/components/status.jsx:3897
msgid "Post removed by author."
msgstr ""

#: src/components/status.jsx:3906
msgid "You’re not authorized to view this post."
msgstr ""

#: src/components/status.jsx:3915
msgid "Post pending author approval."
msgstr ""

#: src/components/status.jsx:3924
msgid "Quoting not allowed by the author."
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Nauji įrašai"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1077
#: src/pages/status.jsx:1454
msgid "Try again"
msgstr "Bandyti dar kartą"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# pasidalinimas} few {# pasidalinimai} many {# pasidalinimo} other {# pasidalinimų}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Prisegti įrašai"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Gija"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Filtruota</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:194
msgid "Auto-translated from {sourceLangText}"
msgstr "Automatiškai išversta iš {sourceLangText}"

#: src/components/translation-block.jsx:232
msgid "Translating…"
msgstr "Verčiama…"

#: src/components/translation-block.jsx:235
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Versti iš {sourceLangText} (automatiškai aptikta)"

#: src/components/translation-block.jsx:236
msgid "Translate from {sourceLangText}"
msgstr "Versti iš {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:264
msgid "Auto ({0})"
msgstr "Automatinis ({0})"

#: src/components/translation-block.jsx:277
msgid "Failed to translate"
msgstr "Nepavyko išversti."

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Šaltinio būsenos redagavimas"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "Atsakant į @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Gali dabar uždaryti šį puslapį."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Uždaryti langą"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Privalomas prisijungimas."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:276
msgid "Go home"
msgstr "Eiti į pagrindinį"

#: src/pages/account-statuses.jsx:238
msgid "Account posts"
msgstr "Paskyros įrašai"

#: src/pages/account-statuses.jsx:245
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ atsakymai)"

#: src/pages/account-statuses.jsx:247
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- pasidalinimai)"

#: src/pages/account-statuses.jsx:249
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:251
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (medija)"

#: src/pages/account-statuses.jsx:257
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:323
msgid "Clear filters"
msgstr "Valyti filtrus"

#: src/pages/account-statuses.jsx:326
msgid "Clear"
msgstr "Valyti"

#: src/pages/account-statuses.jsx:340
msgid "Showing post with replies"
msgstr "Rodomas įrašas su atsakymais"

#: src/pages/account-statuses.jsx:345
msgid "+ Replies"
msgstr "+ atsakymai"

#: src/pages/account-statuses.jsx:351
msgid "Showing posts without boosts"
msgstr "Rodomi įrašai be pasidalinimų"

#: src/pages/account-statuses.jsx:356
msgid "- Boosts"
msgstr "- pasidalinimai"

#: src/pages/account-statuses.jsx:362
msgid "Showing posts with media"
msgstr "Rodomi įrašai su medija"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:379
msgid "Showing posts tagged with #{0}"
msgstr "Rodomi įrašai pažymėti su #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:418
msgid "Showing posts in {0}"
msgstr "Rodomi įrašai rikiavime {0}"

#: src/pages/account-statuses.jsx:508
msgid "Nothing to see here yet."
msgstr "Kol kas nėra čia ką matyti."

#: src/pages/account-statuses.jsx:509
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Nepavyksta įkelti įrašų."

#: src/pages/account-statuses.jsx:550
#: src/pages/account-statuses.jsx:580
msgid "Unable to fetch account info"
msgstr "Nepavyksta gauti paskyros informacijos."

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:557
msgid "Switch to account's instance {0}"
msgstr "Perjungti į paskyros serverį {0}"

#: src/pages/account-statuses.jsx:587
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Perjungti į mano serverį (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:660
msgid "Month"
msgstr "Mėnuo"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Dabartinė"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Numatyta"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Perjungti į šią paskyrą"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Perjungti naujame skirtuke / lange"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Peržiūrėti profilį…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Nustatyti kaip numatytąjį"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Atsijungti iš <0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Atsijungti…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr "Prijungta prie {0} (<0/>)"

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Pridėti esančią paskyrą"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Pastaba: <0>Numatytoji</0> paskyra visada bus naudojama pirmajam įkėlimui. Perjungtos paskyros išliks seanso metu."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Kol kas nėra žymių. Eik kažką pridėti į žymes!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Nepavyksta įkelti žymių."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "paskutinės 1 valandos"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "paskutinių 2 valandų"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "paskutinių 3 valandų"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "paskutinių 4 valandų"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "paskutinių 5 valandų"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "paskutinių 6 valandų"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "paskutinių 7 valandų"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "paskutinių 8 valandų"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "paskutinių 9 valandų"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "paskutinių 10 valandų"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "paskutinių 11 valandų"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "paskutinių 12 valandų"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "po 12 valandų"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Sekamos žymės"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Grupės"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Rodomi {selectedFilterCategory, select, all {visi įrašai} original {originalūs įrašai} replies {atsakymai} boosts {pasidalinimai} followedTags {sekamos žymės} groups {grupės} filtered {filtruojami įrašai}}, {sortBy, select, createdAt {{sortOrder, select, asc {seniausi} desc {naujausi}}} reblogsCount {{sortOrder, select, asc {mažiausiai pasidalinimų} desc {daugiausiai pasidalinimų}}} favouritesCount {{sortOrder, select, asc {mažiausiai patiktukų} desc {daugiausiai patiktukų}}} repliesCount {{sortOrder, select, asc {mažiausiai atsakymų} desc {daugiausiai atsakymų}}} density {{sortOrder, select, asc {mažiausiai tankūs} desc {tankiausi}}}} pirmiausiai {groupBy, select, account {, sugrupuota pagal autorius} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Pasivijimas <0>beta versija</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Žinynas"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Kas tai?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Pasivijimo informacija – tai atskira sekimu laiko skalė, suteikianti aukšto lygio peržiūrą iš pirmo žvilgsnio, su paprasta, el. pašto įkvėpta sąsaja, kad būtų galima lengvai rikiuoti ir filtruoti įrašus."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Pasivijimo naudotojo sąsajos peržiūra"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Pasivykime"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Pasivykime tavo sekimų įrašus."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Rodyti visus įrašus nuo…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "iki didžiausio"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Pasivyti"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Sutampa su paskutiniu pasivijimu"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Iki paskutinio pasivijimo ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Pastaba: tavo serveris gali rodyti ne daugiau kaip 800 įrašų pagrindinėje laiko skalėje, nepaisant laiko intervalo. Gali būti mažiau arba daugiau."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Anksčiau…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# įrašas} few {# įrašai} many {# įrašo} other {# įrašų}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Šalinti šį pasivijimą?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "Pašalinima {0} pasivijimas"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "Pašalintas {0} pasivijimas"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Pastaba: bus išsaugota tik ne daugiau kaip 3. Likusios bus automatiškai pašalintos."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Gaunami įrašai…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Tai gali šiek tiek užtrukti."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Atkurti filtrus"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Populiariausios nuorodos"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Bendrino {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:329
msgid "All"
msgstr "Viskas"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# autorius} few {# autoriai} many {# autoriaus} other {# autorių}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Rikiuoti"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Data"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Tankumas"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "Filtruoti"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Autoriai"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Nieko"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Rodyti visus autorius"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Nebūtina perskaityti viską."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "Tai viskas."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Grįžti į viršų"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Nuorodos bendrinamos pagal sekimus, surikiuotos pagal bendrinamų nuorodų skaičių, pasidalinimus ir patiktukus."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Rikiuoti: tankumas"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Įrašai rikiuojami pagal informacijos tankumą arba gilumą. Trumpesni įrašai „lengvesni“, o ilgesni – „sunkesni“. Įrašai su nuotraukomis yra „sunkesni“ nei įrašai be nuotraukų."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Grupuoti: autoriai"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Įrašai sugrupuoti pagal autorius, surikiuoti pagal kiekvieno autoriaus įrašų skaičių."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Kitas autorius"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Ankstesnis autorius"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Slinkti į viršų"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Kol kas nėra patiktukų. Eik kažką pamėgti!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Nepavyksta įkelti patiktukų."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Pagrindinis ir sąrašai"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Viešieji laiko skalės"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Pokalbiai"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Profiliai"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Niekada"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Naujas filtras"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# filtras} few {# filtrai} many {# filtro} other {# filtrų}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Nepavyksta įkelti filtrų."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Kol kas nėra filtrų."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Pridėti filtrą"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Redaguoti filtrą"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Nepavyksta redaguoti filtro."

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Nepavyksta sukurti filtro."

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Pavadinimas"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Visas žodis"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Nėra raktažodžių. Pridėk vieną."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Pridėti raktažodį"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# raktažodis} few {# raktažodžiai} many {# raktažodžio} other {# raktažodžių}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Filtruoti iš…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Kol kas nepadaryta"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Būsena: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Keisti galiojimo laiką"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Galiojimo laikas"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "Filtruotas įrašas bus…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "uždengta (tik medija)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "sumažintas"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "paslėptas"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Ištrinti šį filtrą?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Nepavyksta ištrinti filtro."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Nebegalioja"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Baigiasi <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Niekada nesibaigia"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# saitažodis} few {# saitažodžiai} many {# saitažodžio} other {# saitažodžių}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Nepavyksta įkelti sekamų saitažodžių."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Kol kas nėra sekamų saitažodžių."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Nėra ką matyti čia."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Nepavyksta įkelti įrašų."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (tik medija) serveryje {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} serveryje {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (tik medija)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Kol kas niekas nieko su šia žyme nepaskelbė."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Nepavyksta įkelti įrašų su šia žyme."

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Nebesekti #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "Nebesekama #{hashtag}"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "Sekama #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Sekama…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Neberodoma profilyje"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Nepavyksta neberodyti profilyje."

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Rodoma profilyje"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, one {Didžiausia # žymė} few {Didžiausia # žymės} many {Didžiausia # žymes}other {Didžiausia # žymių}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Pridėti saitažodį"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Šalinti saitažodį"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {Pasiektas didžiausias # trumpojo nuorodos. Nepavyksta pridėti trumposios nuorodos.} few {Pasiekti didžiausias # trumpųjų nuorodų. Nepavyksta pridėti trumposios nuorodos.} many {Pasiekta didžiausias # trumpojo nuorodos. Nepavyksta pridėti trumposios nuorodos.} other {Pasiekta didžiausias # trumpųjų nuorodų. Nepavyksta pridėti trumposios nuorodos.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Ši nuoroda jau egzistuoja."

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Pridėtas trumpasis nuorodos saitažodis"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Pridėti prie trumpųjų nuorodų"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Įvesk naują serverį, pvz., mastodon.social"

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Netinkamas serveris."

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Eiti į kitą serverį…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Eiti į mano serverį (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Nepavyksta gauti pranešimų."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Nauji</0> <1>sekimo prašymai</1>"

#: src/pages/home.jsx:251
msgid "See all"
msgstr "Žiūrėti viską"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Sprendžiama…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "Nepavyksta išspręsti URL."

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Kol kas nieko."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Tvarkyti narius"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Pašalinti <0>@{0}</0> iš sąrašo?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Šalinti…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# sąrašas} few {# sąrašai} many {# sąrašo} other {# sąrašų}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Kol kas nėra sąrašų."

#: src/pages/login.jsx:118
#: src/pages/login.jsx:128
msgid "Failed to register application"
msgstr "Nepavyko užregistruoti programos."

#: src/pages/login.jsx:214
msgid "instance domain"
msgstr "serverio domenas"

#: src/pages/login.jsx:238
msgid "e.g. “mastodon.social”"
msgstr "pvz., mastodon.social"

#: src/pages/login.jsx:249
msgid "Failed to log in. Please try again or try another instance."
msgstr "Nepavyko prisijungti. Bandyk dar kartą arba išbandyk kitą serverį."

#: src/pages/login.jsx:261
msgid "Continue with {selectedInstanceText}"
msgstr "Tęsti su {selectedInstanceText}"

#: src/pages/login.jsx:262
msgid "Continue"
msgstr "Tęsti"

#: src/pages/login.jsx:270
msgid "Don't have an account? Create one!"
msgstr "Neturi paskyros? Susikurk vieną!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Privatūs paminėjimai"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Privati"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Niekas tavęs nepaminėjo. :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Nepavyksta įkelti paminėjimų."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Neseki"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Kurie tavęs neseka"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "Su nauja paskyra"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Kurie neprašytai privačiai paminėjo tave"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Kurie yra ribojami serverio prižiūrėtojų"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Pranešimų nustatymai"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Nauji pranešimai"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Skelbimas} few {Skelbimai} many {Skelbimo} other {Skelbimų}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Sekimo prašymai"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# sekimo prašymai} few {# sekimo prašymai} many {# sekimo prašymo} other {# sekimo prašymų}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Filtruojami pranešimai iš # žmogaus} few {Filtruojami pranešimai iš # žmonių} many {Filtruojami pranešimai iš # žmogaus} other {Filtruojami pranešimai iš # žmonių}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Tik paminėjimus"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Šiandien"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Viską peržiūrėjai."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Vakar"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Nepavyksta įkelti pranešimų."

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Atnaujinti pranešimų nustatymai"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Filtruoti pranešimus iš žmonių:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Filtruoti"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ignoruoti"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Atnaujinta <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Peržiūrėti pranešimus iš <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Pranešimai iš <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Pranešimai iš @{0} nuo šiol nebus filtruojami."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "Nepavyksta priimti pranešimo prašymo."

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Leisti"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "Pranešimai iš @{0} nuo šiol nebus rodomi filtruotuose pranešimuose."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "Nepavyksta atmesti pranešimo prašymo."

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Atmesti"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Atmesta"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Vietinė laiko skalė ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Federacinė laiko skalė ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Vietinė laiko skalė"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Federacinė laiko skalė"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Kol kas niekas nieko nepaskelbė."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Perjungti į federacinę"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Perjungti į vietinį"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "Nėra suplanuotų įrašų."

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Suplanuota <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Suplanuota <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Suplanuotas įrašas iš naujo"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "Nepavyko suplanuoti įrašo iš naujo."

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Suplanuoti iš naujo"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "Ištrinti suplanuotą įrašą?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "Suplanuotas įrašas ištrintas"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "Nepavyko ištrinti suplanuoto įrašo."

#: src/pages/search.jsx:50
msgid "Search: {q} (Posts)"
msgstr "Paieška: {q} (įrašai)"

#: src/pages/search.jsx:53
msgid "Search: {q} (Accounts)"
msgstr "Paieška: {q} (paskyros)"

#: src/pages/search.jsx:56
msgid "Search: {q} (Hashtags)"
msgstr "Paieška: {q} (saitažodžiai)"

#: src/pages/search.jsx:59
msgid "Search: {q}"
msgstr "Paieška: {q}"

#: src/pages/search.jsx:339
#: src/pages/search.jsx:421
msgid "Hashtags"
msgstr "Saitažodžiai"

#: src/pages/search.jsx:371
#: src/pages/search.jsx:425
#: src/pages/search.jsx:495
msgid "See more"
msgstr "Žiūrėti daugiau"

#: src/pages/search.jsx:397
msgid "See more accounts"
msgstr "Žiūrėti daugiau paskyrų"

#: src/pages/search.jsx:411
msgid "No accounts found."
msgstr "Paskyrų nerasta."

#: src/pages/search.jsx:467
msgid "See more hashtags"
msgstr "Žiūrėti daugiau saitažodžių"

#: src/pages/search.jsx:481
msgid "No hashtags found."
msgstr "Saitažodžių nerasta."

#: src/pages/search.jsx:525
msgid "See more posts"
msgstr "Žiūrėti daugiau įrašų"

#: src/pages/search.jsx:539
msgid "No posts found."
msgstr "Įrašų nerasta."

#: src/pages/search.jsx:583
msgid "Enter your search term or paste a URL above to get started."
msgstr "Įvesk paieškos terminą arba įklijuok URL, kad pradėtum."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Nustatymai"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Išvaizda"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Šviesi"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Tamsi"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Automatinis"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Teksto dydis"

#. Preview of one character, in smallest size
#. Preview of one character, in largest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Rodymo kalba"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Atlikti vertimus savanoriškai"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Skelbimas"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Numatytasis matomumas"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Sinchronizuota"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Nepavyko atnaujinti skelbimo privatumo."

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Sinchronizuota su serverio nustatymais. <0>Eiti į savo serverį ({instance}), kad sužinoti daugiau nustatymų.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Eksperimentai"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Automatiškai atnaujinti laiko skalės įrašus"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Pasidalinimų karuselė"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Įrašo vertimas"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Versti į "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Sistemos kalbą ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, one {Slėpti Versti mygtuką (#):} few {Slėpti Versti mygtuką (#):} many {Slėpti Versti mygtuką (#):}=0 {} other {Slėpti Versti mygtuką (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "Pastaba: ši funkcija naudoja išorines vertimo paslaugas, veikiančias su <0>„{TRANSLATION_API_NAME}“</0>."

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Automatinis įterptinis vertimas"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Automatiškai rodyti vertimą įrašams laiko skalėje. Veikia tik <0>trumpiems</0> įrašams be turinio įspėjimo, medijos ir apklausos."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "GIF parinkiklis rengyklei"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Pastaba: ši funkcija naudoja išorinę GIF paieškos paslaugą, veikiančią su <0>„GIPHY“</0>. „G-rated“ (skirta visiems amžiaus grupėms), sekimo parametrai pašalinami, užklausose nepateikiama nukreipiančioji informacija, bet paieškos užklausos ir IP adreso informacija vis tiek pasieks jų serverius."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Vaizdų aprašymo generatorius"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Tik naujiems vaizdams, kai kuriami nauji įrašai."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Pastaba: ši funkcija naudoja išorinę DI paslaugą, veikiančią su <0>„img-alt-api“</0>. Gali veikti netinkamai. Tik vaizdams ir anglų kalba."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Serverio pusėje sugrupuoti pranešimai"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Alfa stadijos funkcija. Galimai patobulintas grupavimo langas, bet bazinė grupavimo logika."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "„Debesis“ importuoti / eksportuoti trumpųjų nuorodų nustatymus"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Labai eksperimentinis.<0/>Įrašyta jūsų profilio pastabose. Profilio (privačios) pastabos daugiausia naudojamos kitiems profiliams, o savo profilyje yra paslėptos."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Pastaba: ši funkcija naudoja šiuo metu prisijungusio serverio API."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Slėpimo režimas <0>(<1>Tekstas</1> → <2>███████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Pakeiskite tekstą blokais – tai naudinga darant ekrano kopijas dėl privatumo priežasčių."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Apie"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Sukūrė</0> <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Remti"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Aukoti"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr ""

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Privatumo politika"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Svetainė:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Versija:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Nukopijuota versijos eilutė"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "Nepavyksta nukopijuoti versijos eilutės."

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "Nepavyko atnaujinti prenumeratos. Bandykite dar kartą."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "Nepavyko pašalinti prenumeratos. Bandykite dar kartą."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Tiesioginiai pranešimai (beta versija)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Tiesioginiai pranešimai yra užblokuoti. Įjunkite juos naršyklės nustatymuose."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Leisti iš <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "bet kieno"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "žmonių, kuriuos seku"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "sekėjų"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Sekimai"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Apklausos"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Įrašų redagavimai"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "Nuo paskutinio prisijungimo nebuvo suteiktas tiesioginis leidimas. Turėsite <0><1>prisijungti</1> dar kartą, kad suteiktumėte tiesioginį leidimą</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "Pastaba: tiesioginiai pranešimai veikia tik <0>vienai paskyrai</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:657
#: src/pages/status.jsx:1220
msgid "post.title"
msgstr "Įrašas"

#: src/pages/status.jsx:911
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Nesate prisijungę. Sąveikos (atsakyti, pasidalinti ir t. t.) negalimos."

#: src/pages/status.jsx:931
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Šis įrašas yra iš kito serverio (<0>{instance}</0>). Sąveikos (atsakyti, pasidalinti ir t. t.) negalimos."

#: src/pages/status.jsx:959
msgid "Error: {e}"
msgstr "Klaida: {e}"

#: src/pages/status.jsx:966
msgid "Switch to my instance to enable interactions"
msgstr "Perjungti į mano serverį, kad būtų įjungtos sąveikos"

#: src/pages/status.jsx:1068
msgid "Unable to load replies."
msgstr "Nepavyksta įkelti atsakymų."

#: src/pages/status.jsx:1180
msgid "Back"
msgstr "Atgal"

#: src/pages/status.jsx:1211
msgid "Go to main post"
msgstr "Eiti į pagrindinį įrašą"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1234
msgid "{0} posts above ‒ Go to top"
msgstr "{0} įrašų aukščiau – eiti į viršų"

#: src/pages/status.jsx:1281
#: src/pages/status.jsx:1344
msgid "Switch to Side Peek view"
msgstr "Perjungti į šoninio žvilgsnio rodymą"

#: src/pages/status.jsx:1345
msgid "Switch to Full view"
msgstr "Perjungti į visą rodymą"

#: src/pages/status.jsx:1363
msgid "Show all sensitive content"
msgstr "Rodyti visą jautrų turinį"

#: src/pages/status.jsx:1368
msgid "Experimental"
msgstr "Eksperimentinis"

#: src/pages/status.jsx:1377
msgid "Unable to switch"
msgstr "Nepavyksta perjungti."

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1384
msgid "Switch to post's instance ({0})"
msgstr "Perjungti į įrašo serverį ({0})"

#: src/pages/status.jsx:1387
msgid "Switch to post's instance"
msgstr "Perjungti į įrašo serverį"

#: src/pages/status.jsx:1445
msgid "Unable to load post"
msgstr "Nepavyksta įkelti įrašo."

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1581
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# atsakymas} few {<0>{1}</0> atsakymai} many {<0>{1}</0> atsakymo} other {<0>{1}</0> atsakymų}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1599
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# komentaras} few {<0>{0}</0> komentarai} many {<0>{0}</0> komentaro} other {<0>{0}</0> komentarų}}"

#: src/pages/status.jsx:1621
msgid "View post with its replies"
msgstr "Peržiūrėti įrašą su jo atsakymais"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Tendencinga ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Tendencingos naujienos"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "Sukūrė {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Atgal į tendencingų įrašų rodymą"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Rodomi įrašai, kuriuose paminimas <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Tendencingi įrašai"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Nėra tendencingų įrašų."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Minimalistinė „Mastodon“ interneto kliento programa."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Prisijungti su „Mastodon“"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Registruotis"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Prijunkite esamą „Mastodon“ / fediverso paskyrą.<0/>Jūsų kredencialai šiame serveryje nėra saugomi."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Sukūrė</0> <1>@cheeaun</1>. <2>Privatumo politika</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Pasidalinimų karuselės ekrano kopija"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Pasidalinimų karuselė"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Vizualiai atskirkite originalius įrašus ir pakartotinai bendrinamus įrašus (pasidalintus įrašus)."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Įterptų komentarų gijos ekrano kopija"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Įterptų komentarų gija"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Lengvai sekite pokalbius. Pusiau suskleidžiamieji atsakymai."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Sugrupuotų pranešimų ekrano kopija"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Sugrupuoti pranešimai"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Panašūs pranešimai sugrupuojami ir suskleidžiami, kad būtų mažiau netvarkos."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Daugiastulpelių naudotojo sąsajos ekrano kopija"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Vieno arba daugiastulpelių"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Pagal numatytuosius nustatymus – vienas stulpelis zeno režimo ieškikliams. Galimybė konfigūruoti daugiastulpelius patyrusiems naudotojams."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Daugiasaitažodžių laiko skalės ekrano kopija su forma, kuria galima pridėti daugiau saitažodžių"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Daugiasaitažodžių laiko skalė"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Iki 5 saitažodžių, sujungtų į vieną laiko skalę."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Atrodo, kad jūsų naršyklė blokuoja iškylančiuosius langus."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Įrašo juodraštis šiuo metu sumažintas. Paskelbkite arba atmeskite jį prieš sukuriant naują."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Įrašas šiuo metu atidarytas. Paskelbkite arba atmeskite jį prieš sukuriant naują."

