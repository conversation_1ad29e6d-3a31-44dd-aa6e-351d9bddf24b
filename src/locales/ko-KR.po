msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: ko\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-06-20 01:13\n"
"Last-Translator: \n"
"Language-Team: Korean\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: ko\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:137
msgid "Locked"
msgstr "잠김"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:143
msgid "Posts: {0}"
msgstr "게시물: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:148
msgid "Last posted: {0}"
msgstr "마지막 게시일: {0}"

#: src/components/account-block.jsx:163
#: src/components/account-info.jsx:710
msgid "Automated"
msgstr "자동화됨"

#: src/components/account-block.jsx:170
#: src/components/account-info.jsx:715
#: src/components/status.jsx:590
msgid "Group"
msgstr "그룹"

#: src/components/account-block.jsx:180
msgid "Mutual"
msgstr "맞팔"

#: src/components/account-block.jsx:184
#: src/components/account-info.jsx:1875
msgid "Requested"
msgstr "요청함"

#: src/components/account-block.jsx:188
#: src/components/account-info.jsx:1866
msgid "Following"
msgstr "팔로잉"

#: src/components/account-block.jsx:192
#: src/components/account-info.jsx:1180
msgid "Follows you"
msgstr "날 팔로 함"

#: src/components/account-block.jsx:200
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# 팔로워} other {# 팔로워}}"

#: src/components/account-block.jsx:209
#: src/components/account-info.jsx:758
msgid "Verified"
msgstr "인증됨"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:224
#: src/components/account-info.jsx:860
msgid "Joined <0>{0}</0>"
msgstr "<0>{0}</0> 가입"

#: src/components/account-info.jsx:63
msgid "Forever"
msgstr "무기한"

#: src/components/account-info.jsx:403
msgid "Unable to load account."
msgstr "계정을 불러 올 수 없습니다."

#: src/components/account-info.jsx:418
msgid "Go to account page"
msgstr "계정 페이지로 이동"

#: src/components/account-info.jsx:447
#: src/components/account-info.jsx:780
#: src/components/account-info.jsx:810
msgid "Followers"
msgstr "팔로워"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:451
#: src/components/account-info.jsx:820
#: src/components/account-info.jsx:839
msgid "following.stats"
msgstr ""

#: src/components/account-info.jsx:454
#: src/components/account-info.jsx:856
#: src/pages/account-statuses.jsx:487
#: src/pages/search.jsx:344
#: src/pages/search.jsx:491
msgid "Posts"
msgstr "게시물"

#: src/components/account-info.jsx:462
#: src/components/account-info.jsx:1236
#: src/components/compose.jsx:2786
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1836
#: src/components/status.jsx:1853
#: src/components/status.jsx:1978
#: src/components/status.jsx:2599
#: src/components/status.jsx:2602
#: src/pages/account-statuses.jsx:531
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1305
#: src/pages/trending.jsx:474
msgid "More"
msgstr "더 보기"

#: src/components/account-info.jsx:474
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> 님은 새 계정으로 옮기셨습니다:"

#: src/components/account-info.jsx:619
#: src/components/account-info.jsx:1464
msgid "Handle copied"
msgstr "핸들 복사됨"

#: src/components/account-info.jsx:622
#: src/components/account-info.jsx:1467
msgid "Unable to copy handle"
msgstr "핸들을 복사할 수 없습니다"

#: src/components/account-info.jsx:628
#: src/components/account-info.jsx:1473
msgid "Copy handle"
msgstr "핸들 복사"

#: src/components/account-info.jsx:634
msgid "Go to original profile page"
msgstr "원본 프로필 페이지로 가기"

#: src/components/account-info.jsx:652
msgid "View profile image"
msgstr "프로필 이미지 보기"

#: src/components/account-info.jsx:670
msgid "View profile header"
msgstr "프로필 헤더 보기"

#: src/components/account-info.jsx:686
#: src/components/account-info.jsx:1774
#: src/components/account-info.jsx:2300
msgid "Edit profile"
msgstr "프로필 고치기"

#: src/components/account-info.jsx:705
msgid "In Memoriam"
msgstr "고인을 추모함"

#: src/components/account-info.jsx:787
#: src/components/account-info.jsx:830
msgid "This user has chosen to not make this information available."
msgstr "이 사용자는 해당 정보를 볼 수 없도록 설정했습니다."

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:885
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0}개의 원문 게시물, {1}개의 댓글, {2}개의 부스트"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:901
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {지난 하루 동안 1개의 게시물} other {지난 {2}일 동안 1개의 게시물}}} other {{3, plural, one {지난 하루 동안 {4}개의 게시물} other {지난 {6}일 동안 {5}개의 게시물}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:917
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {지난 몇 해 동안 1개의 게시물} other {지난 몇 해 동안 {1}개의 게시물}}"

#: src/components/account-info.jsx:942
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "원본"

#: src/components/account-info.jsx:946
#: src/components/status.jsx:2383
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1024
#: src/pages/status.jsx:1651
msgid "Replies"
msgstr "댓글"

#: src/components/account-info.jsx:950
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "부스트"

#: src/components/account-info.jsx:956
msgid "Post stats unavailable."
msgstr "게시물 통계 못 봄."

#: src/components/account-info.jsx:987
msgid "View post stats"
msgstr "게시물 통계 보기"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1184
msgid "Last post: <0>{0}</0>"
msgstr "마지막 게시물: <0>{0}</0>"

#: src/components/account-info.jsx:1198
msgid "Muted"
msgstr "뮤트됨"

#: src/components/account-info.jsx:1203
msgid "Blocked"
msgstr "차단됨"

#: src/components/account-info.jsx:1212
msgid "Private note"
msgstr ""

#: src/components/account-info.jsx:1269
msgid "Mention <0>@{username}</0>"
msgstr "<0>@{username}</0> 님 언급하기"

#: src/components/account-info.jsx:1281
msgid "Translate bio"
msgstr "소개문 번역"

#: src/components/account-info.jsx:1292
msgid "Edit private note"
msgstr ""

#: src/components/account-info.jsx:1292
msgid "Add private note"
msgstr ""

#: src/components/account-info.jsx:1312
msgid "Notifications enabled for @{username}'s posts."
msgstr ""

#: src/components/account-info.jsx:1313
msgid " Notifications disabled for @{username}'s posts."
msgstr ""

#: src/components/account-info.jsx:1325
msgid "Disable notifications"
msgstr ""

#: src/components/account-info.jsx:1326
msgid "Enable notifications"
msgstr ""

#: src/components/account-info.jsx:1343
msgid "Boosts from @{username} enabled."
msgstr ""

#: src/components/account-info.jsx:1344
msgid "Boosts from @{username} disabled."
msgstr ""

#: src/components/account-info.jsx:1355
msgid "Disable boosts"
msgstr ""

#: src/components/account-info.jsx:1355
msgid "Enable boosts"
msgstr ""

#: src/components/account-info.jsx:1374
msgid "@{username} is no longer featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1384
msgid "@{username} is now featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1392
msgid "Unable to unfeature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1396
msgid "Unable to feature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1405
msgid "Don't feature on profile"
msgstr ""

#: src/components/account-info.jsx:1406
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "프로필에 내보이기"

#: src/components/account-info.jsx:1415
msgid "Show featured profiles"
msgstr ""

#: src/components/account-info.jsx:1430
#: src/components/account-info.jsx:1440
#: src/components/account-info.jsx:2046
msgid "Add/Remove from Lists"
msgstr "리스트에서 더하기·빼기"

#: src/components/account-info.jsx:1490
#: src/components/status.jsx:1258
msgid "Link copied"
msgstr "링크 복사됨"

#: src/components/account-info.jsx:1493
#: src/components/status.jsx:1261
msgid "Unable to copy link"
msgstr "링크를 복사할 수 없음"

#: src/components/account-info.jsx:1499
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1267
#: src/components/status.jsx:3377
msgid "Copy"
msgstr "복사"

#: src/components/account-info.jsx:1514
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1283
msgid "Sharing doesn't seem to work."
msgstr "공유 기능이 작동하지 않습니다."

#: src/components/account-info.jsx:1520
#: src/components/status.jsx:1289
msgid "Share…"
msgstr "공유…"

#: src/components/account-info.jsx:1540
msgid "Unmuted @{username}"
msgstr ""

#: src/components/account-info.jsx:1552
msgid "Unmute <0>@{username}</0>"
msgstr "<0>@{username}</0> 님 뮤트 풀기"

#: src/components/account-info.jsx:1568
msgid "Mute <0>@{username}</0>…"
msgstr "<0>@{username}</0> 님 뮤트…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1600
msgid "Muted @{username} for {0}"
msgstr ""

#: src/components/account-info.jsx:1612
msgid "Unable to mute @{username}"
msgstr ""

#: src/components/account-info.jsx:1633
msgid "Remove <0>@{username}</0> from followers?"
msgstr "<0>@{username}</0> 님을 팔로워에서 뺄까요?"

#: src/components/account-info.jsx:1653
msgid "@{username} removed from followers"
msgstr ""

#: src/components/account-info.jsx:1665
msgid "Remove follower…"
msgstr "팔로워에서 빼기…"

#: src/components/account-info.jsx:1676
msgid "Block <0>@{username}</0>?"
msgstr "<0>@{username}</0> 님을 차단할까요?"

#: src/components/account-info.jsx:1700
msgid "Unblocked @{username}"
msgstr ""

#: src/components/account-info.jsx:1708
msgid "Blocked @{username}"
msgstr ""

#: src/components/account-info.jsx:1716
msgid "Unable to unblock @{username}"
msgstr ""

#: src/components/account-info.jsx:1718
msgid "Unable to block @{username}"
msgstr ""

#: src/components/account-info.jsx:1728
msgid "Unblock <0>@{username}</0>"
msgstr "<0>@{username}</0> 님 차단 풀기"

#: src/components/account-info.jsx:1737
msgid "Block <0>@{username}</0>…"
msgstr "<0>@{username}</0> 님 차단…"

#: src/components/account-info.jsx:1754
msgid "Report <0>@{username}</0>…"
msgstr "<0>@{username}</0> 님 신고…"

#: src/components/account-info.jsx:1810
msgid "Withdraw follow request?"
msgstr ""

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1811
msgid "Unfollow @{0}?"
msgstr ""

#: src/components/account-info.jsx:1869
msgid "Unfollow…"
msgstr "그만 팔로하기…"

#: src/components/account-info.jsx:1878
msgid "Withdraw…"
msgstr "취소…"

#: src/components/account-info.jsx:1885
#: src/components/account-info.jsx:1889
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "팔로"

#: src/components/account-info.jsx:1986
#: src/components/account-info.jsx:2041
#: src/components/account-info.jsx:2175
#: src/components/account-info.jsx:2295
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:891
#: src/components/compose.jsx:2742
#: src/components/compose.jsx:3222
#: src/components/compose.jsx:3431
#: src/components/compose.jsx:3661
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:75
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3101
#: src/components/status.jsx:3341
#: src/components/status.jsx:3850
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1392
msgid "Close"
msgstr "닫기"

#: src/components/account-info.jsx:1991
msgid "Translated Bio"
msgstr "번역된 소개문"

#: src/components/account-info.jsx:2086
msgid "Unable to remove from list."
msgstr ""

#: src/components/account-info.jsx:2087
msgid "Unable to add to list."
msgstr ""

#: src/components/account-info.jsx:2106
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "리스트를 불러 올 수 없음."

#: src/components/account-info.jsx:2110
msgid "No lists."
msgstr "리스트가 없음."

#: src/components/account-info.jsx:2121
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "새 리스트"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2180
msgid "Private note about <0>@{0}</0>"
msgstr "<0>@{0}</0> 님에 관한 비공개 메모"

#: src/components/account-info.jsx:2210
msgid "Unable to update private note."
msgstr ""

#: src/components/account-info.jsx:2233
#: src/components/account-info.jsx:2531
msgid "Cancel"
msgstr "취소"

#: src/components/account-info.jsx:2238
msgid "Save & close"
msgstr "저장하고 닫기"

#: src/components/account-info.jsx:2355
msgid "Unable to update profile."
msgstr ""

#: src/components/account-info.jsx:2362
msgid "Header picture"
msgstr ""

#: src/components/account-info.jsx:2414
msgid "Profile picture"
msgstr ""

#: src/components/account-info.jsx:2466
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "이름"

#: src/components/account-info.jsx:2479
msgid "Bio"
msgstr "소개문"

#: src/components/account-info.jsx:2492
msgid "Extra fields"
msgstr "기타 항목"

#: src/components/account-info.jsx:2498
msgid "Label"
msgstr "레이블"

#: src/components/account-info.jsx:2501
msgid "Content"
msgstr "내용"

#: src/components/account-info.jsx:2534
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "저장"

#: src/components/account-info.jsx:2588
msgid "username"
msgstr "사용자명"

#: src/components/account-info.jsx:2592
msgid "server domain name"
msgstr "서버 도메인 이름"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2658
msgid "Profiles featured by @{0}"
msgstr ""

#: src/components/account-info.jsx:2684
msgid "No featured profiles."
msgstr ""

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "가리기 모드 꺼짐"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "가리기 모드 켜짐"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "홈"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "쓰기"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr ""

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr ""

#: src/components/compose.jsx:211
msgid "Take photo or video"
msgstr ""

#: src/components/compose.jsx:212
msgid "Add media"
msgstr ""

#: src/components/compose.jsx:213
msgid "Add custom emoji"
msgstr "커스텀 에모지 더하기"

#: src/components/compose.jsx:214
msgid "Add GIF"
msgstr ""

#: src/components/compose.jsx:215
msgid "Add poll"
msgstr "설문 넣기"

#: src/components/compose.jsx:216
msgid "Schedule post"
msgstr ""

#: src/components/compose.jsx:416
msgid "You have unsaved changes. Discard this post?"
msgstr "저장되지 않은 변경 사항이 있습니다. 해당 게시물을 지우시겠습니까?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:654
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr ""

#: src/components/compose.jsx:664
#: src/components/compose.jsx:682
#: src/components/compose.jsx:1792
#: src/components/compose.jsx:1917
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {파일은 1개까지만 첨부할 수 있습니다.} other {파일은 #개 까지만 첨부할 수 있습니다.}}"

#: src/components/compose.jsx:872
msgid "Pop out"
msgstr "새 창으로 열기"

#: src/components/compose.jsx:879
msgid "Minimize"
msgstr "최소화"

#: src/components/compose.jsx:915
msgid "Looks like you closed the parent window."
msgstr "Parent window를 닫으신거 같습니다."

#: src/components/compose.jsx:922
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Parent window에 이미 작성 필드가 열려 있고 현재 게시 중인 것 같습니다. 완료될 때까지 기다렸다가 나중에 다시 시도하세요."

#: src/components/compose.jsx:927
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Parent window에 이미 작성 필드가 열려 있는 것 같습니다. 이 창을 열면 Parent window에서 변경한 내용이 취소됩니다. 계속하시겠습니까?"

#: src/components/compose.jsx:970
msgid "Pop in"
msgstr "창 합치기"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:980
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "@{0} 님 게시물에 답글 달기(<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:990
msgid "Replying to @{0}’s post"
msgstr "@{0} 님 게시물에 답글 달기"

#: src/components/compose.jsx:1003
msgid "Editing source post"
msgstr "원본 게시물 고치기"

#: src/components/compose.jsx:1056
msgid "Poll must have at least 2 options"
msgstr "설문에는 적어도 2개 이상의 선택지가 있어야 합니다"

#: src/components/compose.jsx:1060
msgid "Some poll choices are empty"
msgstr "선택지 중에 비어있는 게 있습니다"

#: src/components/compose.jsx:1073
msgid "Some media have no descriptions. Continue?"
msgstr "첨부한 매체 중에 설명이 없는 게 있습니다. 그래도 올릴까요?"

#: src/components/compose.jsx:1125
msgid "Attachment #{i} failed"
msgstr "첨부 파일 #{i} 실패"

#: src/components/compose.jsx:1221
#: src/components/status.jsx:2166
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "열람 주의"

#: src/components/compose.jsx:1237
msgid "Content warning or sensitive media"
msgstr "열람 주의 및 민감한 매체"

#: src/components/compose.jsx:1273
#: src/components/status.jsx:96
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "공개"

#: src/components/compose.jsx:1278
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:97
msgid "Local"
msgstr "로컬"

#: src/components/compose.jsx:1282
#: src/components/status.jsx:98
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "조용히 공개"

#: src/components/compose.jsx:1285
#: src/components/status.jsx:99
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "팔로워만"

#: src/components/compose.jsx:1288
#: src/components/status.jsx:100
#: src/components/status.jsx:2042
msgid "Private mention"
msgstr "쪽지"

#: src/components/compose.jsx:1297
msgid "Post your reply"
msgstr "댓글 달기"

#: src/components/compose.jsx:1299
msgid "Edit your post"
msgstr "게시물 고치기"

#: src/components/compose.jsx:1300
msgid "What are you doing?"
msgstr "지금은 무얼 하고 계신가요?"

#: src/components/compose.jsx:1379
msgid "Mark media as sensitive"
msgstr "민감하다고 표시"

#: src/components/compose.jsx:1416
msgid "Posting on <0/>"
msgstr ""

#: src/components/compose.jsx:1447
#: src/components/compose.jsx:3280
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "더하기"

#: src/components/compose.jsx:1673
msgid "Schedule"
msgstr ""

#: src/components/compose.jsx:1675
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1030
#: src/components/status.jsx:1816
#: src/components/status.jsx:1817
#: src/components/status.jsx:2503
msgid "Reply"
msgstr "댓글"

#: src/components/compose.jsx:1677
msgid "Update"
msgstr "업데이트"

#: src/components/compose.jsx:1678
msgctxt "Submit button in composer"
msgid "Post"
msgstr "올리기"

#: src/components/compose.jsx:1804
msgid "Downloading GIF…"
msgstr "움짤 받는 중…"

#: src/components/compose.jsx:1832
msgid "Failed to download GIF"
msgstr "움짤 받기 실패"

#: src/components/compose.jsx:2047
#: src/components/compose.jsx:2124
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "더 보기…"

#: src/components/compose.jsx:2556
msgid "Uploaded"
msgstr "올라감"

#: src/components/compose.jsx:2569
msgid "Image description"
msgstr "이미지 설명"

#: src/components/compose.jsx:2570
msgid "Video description"
msgstr "동영상 설명"

#: src/components/compose.jsx:2571
msgid "Audio description"
msgstr "오디오 설명"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2606
#: src/components/compose.jsx:2626
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "파일 크기가 너무 큽니다. 올리다가 문제가 생길 수 있습니다. 파일 크기를 {0}에서 {1} 이하로 줄여보세요."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2618
#: src/components/compose.jsx:2638
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr ""

#: src/components/compose.jsx:2646
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "초당 프레임 수가 너무 많습니다. 올리다가 문제가 생길 수 있습니다."

#: src/components/compose.jsx:2706
#: src/components/compose.jsx:2956
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "지우기"

#: src/components/compose.jsx:2723
#: src/compose.jsx:84
msgid "Error"
msgstr "오류"

#: src/components/compose.jsx:2748
msgid "Edit image description"
msgstr "이미지 설명 고치기"

#: src/components/compose.jsx:2749
msgid "Edit video description"
msgstr "동영상 설명 고치기"

#: src/components/compose.jsx:2750
msgid "Edit audio description"
msgstr "오디오 설명 고치기"

#: src/components/compose.jsx:2795
#: src/components/compose.jsx:2844
msgid "Generating description. Please wait…"
msgstr "설명을 자동 생성중. 잠시 기다려 주세요…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2815
msgid "Failed to generate description: {0}"
msgstr ""

#: src/components/compose.jsx:2816
msgid "Failed to generate description"
msgstr "설명을 자동 생성하는 데 실패"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2834
#: src/components/compose.jsx:2880
msgid "Generate description…"
msgstr "설명 자동 생성…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2867
msgid "Failed to generate description{0}"
msgstr ""

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2882
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— 시범중</0>"

#: src/components/compose.jsx:2901
msgid "Done"
msgstr "완료"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2937
msgid "Choice {0}"
msgstr ""

#: src/components/compose.jsx:2984
msgid "Multiple choices"
msgstr "선다형 질문"

#: src/components/compose.jsx:2987
msgid "Duration"
msgstr "기간"

#: src/components/compose.jsx:3018
msgid "Remove poll"
msgstr "설문 지우기"

#: src/components/compose.jsx:3239
msgid "Search accounts"
msgstr ""

#: src/components/compose.jsx:3293
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "계정 불러오기 오류"

#: src/components/compose.jsx:3437
msgid "Custom emojis"
msgstr "커스텀 에모지"

#: src/components/compose.jsx:3457
msgid "Search emoji"
msgstr ""

#: src/components/compose.jsx:3488
msgid "Error loading custom emojis"
msgstr "커스텀 에모지 불러오기 오류"

#: src/components/compose.jsx:3499
msgid "Recently used"
msgstr ""

#: src/components/compose.jsx:3500
msgid "Others"
msgstr ""

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3538
msgid "{0} more…"
msgstr "{0}개 더…"

#: src/components/compose.jsx:3676
msgid "Search GIFs"
msgstr ""

#: src/components/compose.jsx:3691
msgid "Powered by GIPHY"
msgstr ""

#: src/components/compose.jsx:3699
msgid "Type to search GIFs"
msgstr "움짤을 검색하려면 입력하세요"

#: src/components/compose.jsx:3797
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "이전"

#: src/components/compose.jsx:3815
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "다음"

#: src/components/compose.jsx:3832
msgid "Error loading GIFs"
msgstr "GIF 불러오기 오류"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "올리지 않은 초고"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "아직 올리지 않은 초고가 있는 것 같습니다. 쓰다 만 곳에서 계속하세요."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "이 초고를 지울까요?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "초고를 지우다가 오류가 났습니다! 다시 한 번 시도해 보세요."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1433
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "지우기…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "댓글 달 게시물을 불러 올 수 없습니다!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "모든 초고를 지울까요?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "초고를 지우다가 오류가 나았습니다! 다시 한 번 시도해 보세요."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "모두 지우기…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "초고를 찾을 수 없었습니다."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "설문"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:367
msgid "Media"
msgstr "매체"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "새 창에서 열기"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "수락"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "거절"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "수락함"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "거절함"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:334
#: src/pages/search.jsx:367
msgid "Accounts"
msgstr "계정"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:561
#: src/pages/status.jsx:1425
msgid "Show more…"
msgstr "더 보기…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:566
msgid "The end."
msgstr "끝"

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "표시할 내용 없음"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "키보드 단축키"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "키보드 단축키 도움말"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "다음 게시물"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "이전 게시물"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "다음 게시물로 캐러셀 넘기기"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Shift</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "이전 게시물로 캐러셀 넘기기"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Shift</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "새 게시물 불러오기"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "게시물 자세히 보기"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> 또는 <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "열람 주의를 펼치거나<0/>글타래 펼치기·접기"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "게시물 혹은 창 닫기"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> 또는 <1>Backspace</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "멀티 칼럼 모드에서 특정 칼럼으로 이동"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> 에서 <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "새 게시물 쓰기"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "새 게시물 쓰기 (새 창)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Shift</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "게시물 올리기"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> 또는 <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:73
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:46
#: src/pages/search.jsx:316
msgid "Search"
msgstr "검색"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "댓글 (새 창)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Shift</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "좋아요 (즐겨찾기)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> 또는 <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1038
#: src/components/status.jsx:2530
#: src/components/status.jsx:2553
#: src/components/status.jsx:2554
msgid "Boost"
msgstr "부스트"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Shift</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1101
#: src/components/status.jsx:2578
#: src/components/status.jsx:2579
msgid "Bookmark"
msgstr "책갈피"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "가리기 모드 켜고 끄기"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Shift</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "리스트 고치기"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "리스트를 고칠 수 없습니다."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "리스트를 만들 수 없습니다."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "리스트 구성원에게 단 댓글 보기"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "내가 팔로하는 사용자에게 단 댓글 보기"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "댓글은 숨기기"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "이 리스트의 게시물은 첫 화면 및 팔로잉 타임라인에서 가리기"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "만들기"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "리스트를 지울까요?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "리스트를 지울 수 없습니다."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr ""

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "매체 설명"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1144
#: src/components/status.jsx:1153
#: src/components/translation-block.jsx:237
msgid "Translate"
msgstr "번역"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1172
msgid "Speak"
msgstr "말하기"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "원본 매체 새 창에서 열기"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "원본 매체 열기"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "이미지 설명을 생성중입니다. 잠시 기다려 주세요…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "이미지 설명 생성 실패"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "이미지 설명…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "게시물 보기"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "민감한 매체"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "필터됨: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3680
#: src/components/status.jsx:3776
#: src/components/status.jsx:3854
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "필터된"

#: src/components/media.jsx:469
msgid "Open file"
msgstr ""

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr ""

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "게시물이 올라갔습니다. 확인 해 보세요."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr ""

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "댓글이 올라갔습니다. 확인 해 보세요."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "게시물이 고쳐졌습니다. 확인 해 보세요."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "메뉴"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "새로 고침하여 업데이트 할까요?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "새 버전이 올라왔습니다…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr ""

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "따라잡기"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "언급"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "알림"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "신규"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "프로필"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "책갈피"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "좋아요"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "팔로하는 해시태그"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:333
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "필터"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "뮤트한 사용자"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "뮤트한 사용자들…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "차단한 사용자"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "차단한 사용자들…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "계정들…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:195
#: src/pages/status.jsx:924
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "로그인"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "인기"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "연합"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "바로 가기·칼럼…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "설정…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "리스트"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "모든 리스트"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "알림"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "이 알림은 나의 다른 계정에서 왔습니다."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "모든 알림 보기"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} 님이 내 게시물에 {emojiObject} 반응을 남겼습니다."

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} 님이 게시물을 올렸습니다."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} 님이 내 댓글을 부스트했습니다.} other {{account} 님이 내 게시물을 부스트했습니다.}}} other {{account} 님이 내 게시물 {postsCount}개를 부스트했습니다.}}} other {{postType, select, reply {<0><1>{0}</1> 사람</0>이 내 댓글을 부스트했습니다.} other {<2><3>{1}</3> 사람</2>이 내 게시물을 부스트했습니다.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, =1 {{account} 님이 나를 팔로합니다.} other {<0><1>{0}</1> 사람</0>이 나를 팔로합니다.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} 님이 나를 팔로하고 싶어합니다."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} 님이 내 댓글을 좋아합니다.} other {{account} 님이 내 게시물을 좋아합니다.}}} other {{account} 님이 내 게시물 {postsCount}개를 좋아합니다.}}} other {{postType, select, reply {<0><1>{0}</1> 사람</0>이 내 댓글을 좋아합니다.} other {<2><3>{1}</3> 사람</2>이 내 게시물을 좋아합니다.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "만들었거나 투표한 설문 조사가 끝났습니다."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "만든 설문 조사가 끝났습니다."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "투표한 설문 조사가 끝났습니다."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "좋아했거나 부스트 한 게시물이 고쳐졌습니다."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account}님이 내 댓글을 부스트 및 좋아합니다.} other {{account}님이 내 게시물을 부스트 및 좋아합니다.}}} other {{account}님이 내 게시물 {postsCount}개를 부스트 및 좋아함.}}} other {{postType, select, reply {<0><1>{0}</1>명의 사람들이</0> 내 댓글을 부스트 및 좋아합니다.} other {<2><3>{1}</3>명의 사람들이</2> 내 게시물을 부스트 및 좋아합니다.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} 님이 가입했습니다."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} 님이 {targetAccount} 님을 신고했습니다."

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "<0>{name}</0> 서버와 연결 끊김."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "조정 경고"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr ""

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "<0>{from}</0> 의 관리자가 <1>{targetName}</1> 를 일시 중단하였습니다, 이는 더 이상 업데이트를 받거나 상호 작용할 수 없다는 의미입니다."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "<0>{from}</0>의 관리자가 <1>{targetName}</1>를 차단하였습니다. 영향을 받는 팔로워: {followersCount}, 팔로잉: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "당신은 <0>{targetName}</0>를 차단하였습니다. 제거된 팔로워: {followersCount}, 팔로잉: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "중재 경고를 받았습니다."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "계정이 비활성화되었습니다."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "일부 게시물이 민감한 게시물로 처리되었습니다."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "내 게시물 몇 개가 지워졌습니다."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "앞으로의 게시물은 민감하다고 표시됩니다."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "계정이 제한되었습니다."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "계정이 정지되었습니다."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[알 수 없는 알림 유형: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1115
#: src/components/status.jsx:1125
msgid "Boosted/Liked by…"
msgstr "부스트·좋아한 사용자…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "좋아한 사람:"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "부스트한 사람:"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "팔로한 사람:"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "자세히 보기 <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr ""

#: src/components/notification.jsx:801
#: src/components/status.jsx:328
msgid "Read more →"
msgstr "더 보기 →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "투표함"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# vote} other {# votes}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "결과 숨기기"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "투표"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1294
#: src/pages/status.jsx:1317
msgid "Refresh"
msgstr "새로 고침"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "결과 보기"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, other {총 <1>{1}</1>표}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, other {총 <1>{1}</1>명}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "<0/>에 마감"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "마감"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "<0/> 내 마감"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "마감"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}초"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}분"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}시간"

#: src/components/report-modal.jsx:29
msgid "Spam"
msgstr "스팸"

#: src/components/report-modal.jsx:30
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "악성 링크, 허위 참여 또는 반복적인 댓글"

#: src/components/report-modal.jsx:33
msgid "Illegal"
msgstr "불법"

#: src/components/report-modal.jsx:34
msgid "Violates the law of your or the server's country"
msgstr "국내법 또는 서버 국가의 법을 위반"

#: src/components/report-modal.jsx:37
msgid "Server rule violation"
msgstr "서버 규칙 위반"

#: src/components/report-modal.jsx:38
msgid "Breaks specific server rules"
msgstr "특정 서버 규칙 위반"

#: src/components/report-modal.jsx:39
msgid "Violation"
msgstr "위반"

#: src/components/report-modal.jsx:42
msgid "Other"
msgstr "기타"

#: src/components/report-modal.jsx:43
msgid "Issue doesn't fit other categories"
msgstr "어느 유형에도 속하지 않음"

#: src/components/report-modal.jsx:68
msgid "Report Post"
msgstr "게시물 신고"

#: src/components/report-modal.jsx:68
msgid "Report @{username}"
msgstr "@{username} 님 신고"

#: src/components/report-modal.jsx:104
msgid "Pending review"
msgstr "검토 대기중"

#: src/components/report-modal.jsx:146
msgid "Post reported"
msgstr "게시물 신고함"

#: src/components/report-modal.jsx:146
msgid "Profile reported"
msgstr "프로필 신고함"

#: src/components/report-modal.jsx:154
msgid "Unable to report post"
msgstr "게시물을 신고할 수 없음"

#: src/components/report-modal.jsx:155
msgid "Unable to report profile"
msgstr "프로필을 신고할 수 없음"

#: src/components/report-modal.jsx:163
msgid "What's the issue with this post?"
msgstr "이 게시물에 어떤 문제가 있나요?"

#: src/components/report-modal.jsx:164
msgid "What's the issue with this profile?"
msgstr "이 프로필에 어떤 문제가 있나요?"

#: src/components/report-modal.jsx:233
msgid "Additional info"
msgstr "추가 정보"

#: src/components/report-modal.jsx:256
msgid "Forward to <0>{domain}</0>"
msgstr "<0>{domain}</0>에 전달"

#: src/components/report-modal.jsx:266
msgid "Send Report"
msgstr "신고하기"

#: src/components/report-modal.jsx:275
msgid "Muted {username}"
msgstr "{username} 님 뮤트함"

#: src/components/report-modal.jsx:278
msgid "Unable to mute {username}"
msgstr "{username} 님을 뮤트할 수 없음"

#: src/components/report-modal.jsx:283
msgid "Send Report <0>+ Mute profile</0>"
msgstr "신고 <0>및 프로필 뮤트</0>"

#: src/components/report-modal.jsx:294
msgid "Blocked {username}"
msgstr "{username} 님 차단함"

#: src/components/report-modal.jsx:297
msgid "Unable to block {username}"
msgstr "{username} 님을 차단할 수 없음"

#: src/components/report-modal.jsx:302
msgid "Send Report <0>+ Block profile</0>"
msgstr "신고 <0>및 프로필 차단</0>"

#: src/components/search-form.jsx:203
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ 계정, 해시태그 & 포스트</0>"

#: src/components/search-form.jsx:216
msgid "Posts with <0>{query}</0>"
msgstr "<0>{query}</0>이/가 포함된 게시물"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:228
msgid "Posts tagged with <0>#{0}</0>"
msgstr "<0>#{0}</0>로 태그된 게시물"

#: src/components/search-form.jsx:242
msgid "Look up <0>{query}</0>"
msgstr "<0>{query}</0> 찾기"

#: src/components/search-form.jsx:253
msgid "Accounts with <0>{query}</0>"
msgstr "<0>{query}</0>이/가 포함된 계정"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "홈·팔로잉"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "공개(로컬/연합)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "계정"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "해시태그"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "리스트 ID"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "로컬만"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:199
msgid "Instance"
msgstr "인스턴스"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "생략 가능 (예: mastodon.social)"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "검색어"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "멀티칼럼 모드가 아니면 생략 가능"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "예: 픽셀아트 (최대 5개, 띄어쓰기로 구분)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "매체만"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "바로 가기"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "베타"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "표시할 바로 가기 목록을 지정합니다:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "구석에 떠 있는 버튼"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "탭·메뉴 바"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "멀티칼럼"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "현재 보기 모드에서는 못 씀"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "위로 올리기"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "아래로 내리기"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1395
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "고치기"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "바로 가기/열을 두 개 이상 추가하여 작동하도록 합니다."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "아직 아무 칼럼도 없습니다. 칼럼 추가 버튼을 눌러 보세요."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "아직 아무 바로 가기도 없습니다. 바로 가기 추가 버튼을 눌러 보세요."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "뭘 넣어야 할 지 모르겠나요?<0/>우선 <1>홈·팔로잉과 알림</1>부터 넣어보세요."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "최대 {SHORTCUTS_LIMIT} 칼럼"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "최다 {SHORTCUTS_LIMIT}개의 바로 가기"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "가져오기·내보내기"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "칼럼 추가…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "바로 가기 추가…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr ""

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "멀티 칼럼 모드의 경우 검색어가 필수이며, 그렇지 않으면 칼럼이 표시되지 않습니다."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "멀티 해시태그가 지원됩니다. (띄어쓰기로 구분)"

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "바로 가기 고치기"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "바로 가기 추가"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "타임라인"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "리스트"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "<0>바로 가기</0> 가져오기·내보내기"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "가져오기"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "여기에 바로 가기를 붙이세요"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "인스턴스 서버에서 저장된 바로 가기를 받는 중…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "바로 가기 받을 수 없음"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "인스턴스 서버에서 바로 가기 받기"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* 현재 바로 가기에 이미 있음"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "다른 계정에서 온 리스트는 못 가져올 수 있습니다."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "잘못된 설정 형식"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "현재 바로 가기에 덧붙일까요?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr ""

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "가져올 바로가기가 없습니다"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "바로 가기를 가져왔습니다. 최다치인 {SHORTCUTS_LIMIT}개를 넘겼으므로, 나머지는 가져오지 못했습니다."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "바로가기 복사됨"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "가져와서 덧붙이기…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "현재 바로가기를 덮어쓸까요?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "바로 가기를 가져올까요?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "또는 덮어쓰기…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "가져오기…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "내보내기"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "바로 가기 복사됨"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "바로 가기를 복사할 수 없음"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "바로 가기 설정을 복사함"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "바로 가기 설정을 복사할 수 없음"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "공유"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "바로 가기를 인스턴스 서버에 저장중…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "바로 가기 저장됨"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "바로 가기를 저장할 수 없음"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "인스턴스 서버에 동기화"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, other {# 글자}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "바로 가기 JSON 코드"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "인스턴스 서버에서 설정 가져오기·인스턴스 서버에 설정 내보내기 (매우 시범적)"

#: src/components/status.jsx:614
msgid "<0/> <1>boosted</1>"
msgstr "<0/> 님이 <1>부스트 함</1>"

#: src/components/status.jsx:713
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "죄송합니다. 현재 로그인한 인스턴스는 다른 인스턴스에 있는 이 게시물과 상호작용할 수 없습니다."

#. placeholder {0}: username || acct
#: src/components/status.jsx:867
msgid "Unliked @{0}'s post"
msgstr "@{0} 님의 게시물을 좋아했던 것 취소"

#. placeholder {0}: username || acct
#: src/components/status.jsx:868
msgid "Liked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:907
msgid "Unbookmarked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:908
msgid "Bookmarked @{0}'s post"
msgstr ""

#: src/components/status.jsx:1007
msgid "Some media have no descriptions."
msgstr "첨부한 매체 중에 설명이 없는 게 있습니다."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1014
msgid "Old post (<0>{0}</0>)"
msgstr "오래된 게시물 (<0>{0}</0>)"

#: src/components/status.jsx:1038
#: src/components/status.jsx:1078
#: src/components/status.jsx:2530
#: src/components/status.jsx:2553
msgid "Unboost"
msgstr "부스트 취소"

#: src/components/status.jsx:1054
#: src/components/status.jsx:2545
msgid "Quote"
msgstr "인용"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1066
#: src/components/status.jsx:1532
msgid "Unboosted @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1067
#: src/components/status.jsx:1533
msgid "Boosted @{0}'s post"
msgstr ""

#: src/components/status.jsx:1079
msgid "Boost…"
msgstr "부스트…"

#: src/components/status.jsx:1091
#: src/components/status.jsx:1826
#: src/components/status.jsx:2566
msgid "Unlike"
msgstr "좋아요 취소"

#: src/components/status.jsx:1092
#: src/components/status.jsx:1826
#: src/components/status.jsx:1827
#: src/components/status.jsx:2566
#: src/components/status.jsx:2567
msgid "Like"
msgstr "좋아요"

#: src/components/status.jsx:1101
#: src/components/status.jsx:2578
msgid "Unbookmark"
msgstr "책갈피 빼기"

#: src/components/status.jsx:1184
msgid "Post text copied"
msgstr ""

#: src/components/status.jsx:1187
msgid "Unable to copy post text"
msgstr ""

#: src/components/status.jsx:1193
msgid "Copy post text"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1211
msgid "View post by <0>@{0}</0>"
msgstr "<0>@{0}</0> 님의 게시물 보기"

#: src/components/status.jsx:1232
msgid "Show Edit History"
msgstr "수정 내역 보기"

#: src/components/status.jsx:1235
msgid "Edited: {editedDateText}"
msgstr "{editedDateText}에 고쳐짐"

#: src/components/status.jsx:1302
#: src/components/status.jsx:3346
msgid "Embed post"
msgstr "게시물 임베드하기"

#: src/components/status.jsx:1316
msgid "Conversation unmuted"
msgstr "대화 뮤트 풀림"

#: src/components/status.jsx:1316
msgid "Conversation muted"
msgstr "대화 뮤트됨"

#: src/components/status.jsx:1322
msgid "Unable to unmute conversation"
msgstr "대화 뮤트를 풀 수 없음"

#: src/components/status.jsx:1323
msgid "Unable to mute conversation"
msgstr "대화를 뮤트할 수 없음"

#: src/components/status.jsx:1332
msgid "Unmute conversation"
msgstr "대화 뮤트 풀기"

#: src/components/status.jsx:1339
msgid "Mute conversation"
msgstr "대화 뮤트하기"

#: src/components/status.jsx:1355
msgid "Post unpinned from profile"
msgstr "프로필에 고정됐던 게시물을 내림"

#: src/components/status.jsx:1356
msgid "Post pinned to profile"
msgstr "게시물이 프로필에 고정됨"

#: src/components/status.jsx:1361
msgid "Unable to unpin post"
msgstr "게시물 고정을 풀 수 없음"

#: src/components/status.jsx:1361
msgid "Unable to pin post"
msgstr "게시물을 고정할 수 없음"

#: src/components/status.jsx:1370
msgid "Unpin from profile"
msgstr "프로필에 고정된 게시물 내리기"

#: src/components/status.jsx:1377
msgid "Pin to profile"
msgstr "프로필에 고정"

#: src/components/status.jsx:1406
msgid "Delete this post?"
msgstr "게시물을 지울까요?"

#: src/components/status.jsx:1422
msgid "Post deleted"
msgstr "게시물 지워짐"

#: src/components/status.jsx:1425
msgid "Unable to delete post"
msgstr "게시물 지울 수 없음"

#: src/components/status.jsx:1453
msgid "Report post…"
msgstr "게시물 신고…"

#: src/components/status.jsx:1827
#: src/components/status.jsx:1863
#: src/components/status.jsx:2567
msgid "Liked"
msgstr "좋아함"

#: src/components/status.jsx:1860
#: src/components/status.jsx:2554
msgid "Boosted"
msgstr "부스트함"

#: src/components/status.jsx:1870
#: src/components/status.jsx:2579
msgid "Bookmarked"
msgstr "책갈피 꽂음"

#: src/components/status.jsx:1874
msgid "Pinned"
msgstr "고정됨"

#: src/components/status.jsx:1920
#: src/components/status.jsx:2391
msgid "Deleted"
msgstr "지워짐"

#: src/components/status.jsx:1961
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, other {#개의 댓글}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2051
msgid "Thread{0}"
msgstr "글타래{0}"

#: src/components/status.jsx:2129
#: src/components/status.jsx:2191
#: src/components/status.jsx:2287
msgid "Show less"
msgstr "접기"

#: src/components/status.jsx:2129
#: src/components/status.jsx:2191
msgid "Show content"
msgstr "내용 보기"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2283
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "필터됨: {0}"

#: src/components/status.jsx:2287
msgid "Show media"
msgstr "매체 보기"

#: src/components/status.jsx:2427
msgid "Edited"
msgstr "고쳐짐"

#: src/components/status.jsx:2504
msgid "Comments"
msgstr "댓글들"

#. More from [Author]
#: src/components/status.jsx:2804
msgid "More from <0/>"
msgstr ""

#: src/components/status.jsx:3106
msgid "Edit History"
msgstr "수정 내역"

#: src/components/status.jsx:3110
msgid "Failed to load history"
msgstr "내역 불러오기 실패"

#: src/components/status.jsx:3115
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "불러오는 중…"

#: src/components/status.jsx:3351
msgid "HTML Code"
msgstr "HTML 코드"

#: src/components/status.jsx:3368
msgid "HTML code copied"
msgstr "HTML 코드 복사됨"

#: src/components/status.jsx:3371
msgid "Unable to copy HTML code"
msgstr "HTML 코드를 복사하지 못 함"

#: src/components/status.jsx:3383
msgid "Media attachments:"
msgstr "첨부된 매체:"

#: src/components/status.jsx:3405
msgid "Account Emojis:"
msgstr "계정 에모지:"

#: src/components/status.jsx:3436
#: src/components/status.jsx:3481
msgid "static URL"
msgstr "정적 URL"

#: src/components/status.jsx:3450
msgid "Emojis:"
msgstr "에모지:"

#: src/components/status.jsx:3495
msgid "Notes:"
msgstr "메모:"

#: src/components/status.jsx:3499
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "정적이며 스타일이나 JavaScript가 적용되지 않습니다. 필요에 따라 직접 스타일을 적용하시거나 고쳐서 쓰셔야 합니다."

#: src/components/status.jsx:3505
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "설문 조사는 상호작용하지 않으며, 투표수가 고정된 목록으로 표현됩니다."

#: src/components/status.jsx:3510
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "첨부 매체는 이미지나 동영상, 오디오 등 아무 파일이나 됩니다."

#: src/components/status.jsx:3516
msgid "Post could be edited or deleted later."
msgstr "원본 게시물은 나중에 고쳐지거나 지워질 수 있습니다."

#: src/components/status.jsx:3522
msgid "Preview"
msgstr "미리 보기"

#: src/components/status.jsx:3531
msgid "Note: This preview is lightly styled."
msgstr "참고로 위 미리 보기는 다소 스타일이 적용되어 있습니다."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3784
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> 님이 부스트 함"

#: src/components/status.jsx:3897
msgid "Post removed by author."
msgstr ""

#: src/components/status.jsx:3906
msgid "You’re not authorized to view this post."
msgstr ""

#: src/components/status.jsx:3915
msgid "Post pending author approval."
msgstr ""

#: src/components/status.jsx:3924
msgid "Quoting not allowed by the author."
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "새 게시물"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1077
#: src/pages/status.jsx:1454
msgid "Try again"
msgstr "재시도"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr ""

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr ""

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "글타래"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>필터됨</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:194
msgid "Auto-translated from {sourceLangText}"
msgstr "{sourceLangText}에서 자동 번역됨"

#: src/components/translation-block.jsx:232
msgid "Translating…"
msgstr "번역중…"

#: src/components/translation-block.jsx:235
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "{sourceLangText}(자동 인식됨)를 번역"

#: src/components/translation-block.jsx:236
msgid "Translate from {sourceLangText}"
msgstr "{sourceLangText}를 번역"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:264
msgid "Auto ({0})"
msgstr "자동 ({0})"

#: src/components/translation-block.jsx:277
msgid "Failed to translate"
msgstr "번역 실패"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "소스 상태 고치기"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "@{0} 님께 댓글"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "이제 이 페이지를 닫아도 됩니다."

#: src/compose.jsx:71
msgid "Close window"
msgstr "창 닫기"

#: src/compose.jsx:87
msgid "Login required."
msgstr "로그인이 필요합니다."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:276
msgid "Go home"
msgstr "홈 가기"

#: src/pages/account-statuses.jsx:238
msgid "Account posts"
msgstr "계정 게시물"

#: src/pages/account-statuses.jsx:245
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ 댓글)"

#: src/pages/account-statuses.jsx:247
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- 부스트)"

#: src/pages/account-statuses.jsx:249
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:251
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (매체)"

#: src/pages/account-statuses.jsx:257
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:323
msgid "Clear filters"
msgstr "필터 초기화"

#: src/pages/account-statuses.jsx:326
msgid "Clear"
msgstr "초기화"

#: src/pages/account-statuses.jsx:340
msgid "Showing post with replies"
msgstr "댓글이 달린 게시물 보기"

#: src/pages/account-statuses.jsx:345
msgid "+ Replies"
msgstr "+ 댓글"

#: src/pages/account-statuses.jsx:351
msgid "Showing posts without boosts"
msgstr "부스트 빼고 게시물 보는 중"

#: src/pages/account-statuses.jsx:356
msgid "- Boosts"
msgstr "- 부스트"

#: src/pages/account-statuses.jsx:362
msgid "Showing posts with media"
msgstr "매체와 함께 게시글 보여주기"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:379
msgid "Showing posts tagged with #{0}"
msgstr "#{0} 으로 태그된 게시물 보여주기"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:418
msgid "Showing posts in {0}"
msgstr ""

#: src/pages/account-statuses.jsx:508
msgid "Nothing to see here yet."
msgstr "아직 표시할 내용이 없습니다."

#: src/pages/account-statuses.jsx:509
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "게시물을 불러 올 수 없습니다"

#: src/pages/account-statuses.jsx:550
#: src/pages/account-statuses.jsx:580
msgid "Unable to fetch account info"
msgstr "계정 정보를 가져올 수 없음"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:557
msgid "Switch to account's instance {0}"
msgstr "해당 계정의 인스턴스로 전환 {0}"

#: src/pages/account-statuses.jsx:587
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "내 인스턴스로 전환 (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:660
msgid "Month"
msgstr "월"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "현재 계정"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "기본 계정"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "이 계정으로 전환"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "새 탭/창으로 변경하기"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "프로필 보기..."

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "기본 계정으로 삼기"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "로그아웃 <0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "로그아웃…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr ""

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "다른 계정 추가"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "참고: <0>기본 계정</0>은 언제나 가장 먼저 뜹니다. 교체한 계정들은 세션 내내 유지됩니다."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "아직 북마크한 것이 없습니다. 북마크 하나 해보세요!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "책갈피를 불러 올 수 없습니다."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "지난 1시간"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "지난 2시간"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "지난 3시간"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "지난 4시간"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "지난 5시간"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "지난 6시간"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "지난 7시간"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "지난 8시간"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "지난 9시간"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "지난 10시간"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "지난 11시간"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "지난 12시간"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "12시간 이상"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "팔로우한 태그"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "그룹"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr ""

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "따라잡기 <0>베타</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "도움말"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "이게 무엇인가요?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "따라잡기는 이메일에서 영감을 받은 간단한 인터페이스의 별도 타임라인으로, 게시물들이 간편하게 정리 및 필터링되어 한눈에 파악할 수 있는 인터페이스입니다."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "따라잡기 미리 보기"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "따라잡아 볼까요?"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "내가 팔로하는 게시물들을 따라잡아 봅시다."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "다음 기간의 모든 게시물을 봅니다:"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "최대한 많이"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "따라잡기"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "마지막 따라잡기와 기간이 겹칩니다"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "마지막 따라잡기 때({0})까지"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "주의: 인스턴스가 기간 설정과 무관하게 타임라인에서 최대 800개(또는 내외)의 게시물까지만 보여줄 수도 있습니다."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "이전 따라잡기:"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, other {게시물 #개}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "이 따라잡기를 지울까요?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr ""

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr ""

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "참고: 총 3개까지만 보존됩니다. 나머지는 알아서 지워집니다."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "게시물 불러오는 중…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "시간이 조금 걸릴 수 있습니다."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "필터 초기화"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "인기 링크"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "{0} 이 공유함"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:329
msgid "All"
msgstr "전체"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, other {글쓴이 #명}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "정렬"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "날짜"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "밀도"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr ""

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "글쓴이"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "안 묶음"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "모든 글쓴이 보기"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "다 읽을 필요는 없답니다."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "이게 다입니다."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "맨 위로 올라가기"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "팔로한 사람들이 공유한 링크를 공유·부스트·좋아요 수가 많은 순서로 보여줍니다."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "정렬: 밀도"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "게시물을 정보 밀도가 높거나 낮은 순서로 보여줍니다. 짧은 게시물은 정보 밀도가 “낮고”, 긴 게시물은 “높다”고 봅니다. 이미지가 첨부된 게시물은 이미지가 없는 게시물보다 정보 밀도가 “높다”고 봅니다."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "묶기: 글쓴이"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "글이 글쓴이에 따라 묶이며, 게시물이 많은 글쓴이가 앞에 나옵니다."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "다음 글쓴이"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "이전 글쓴이"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "맨 위로"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "아직 좋아요가 없습니다. 좋아요를 눌러보세요!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "좋아요를 불러올 수 없음"

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "홈 및 리스트"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "공개 타임라인"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "대화"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "프로필"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "안 함"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "새 필터"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr ""

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "필터를 로드할 수 없음."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "아직 아무 필터도 없습니다."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "필터 추가"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "필터 고치기"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "필터를 고칠 수 없음"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "필터를 생성할 수 없음"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "제목"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "전체 단어"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "검색어가 없으니 추가하세요."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "검색어 추가"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, other {검색어 #개}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "…로 부터 필터"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "※ 아직 미구현"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "상태: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "만기 변경"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "만기"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "필터된 게시글은..."

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr ""

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "최소화됨"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "숨김"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "이 필터를 지울까요?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "필터를 지울 수 없습니다."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "만료됨"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "<0/> 만료됨"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "만기 없음"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, other {해시태그 #개}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "팔로하는 해시태그를 불러 올 수 없습니다."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "아직 아무 해시태그도 팔로하지 않습니다."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "표시할 내용이 없습니다."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "게시물을 불러 올 수 없습니다."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{instance} 상의 {hashtagTitle} (매체만)"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{instance} 상의 {hashtagTitle}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (매체만)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr ""

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "해당 태그를 포함한 게시물을 불러올 수 없습니다"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "#{hashtag} 를 그만 팔로우할까요?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "#{hashtag} 팔로 풂"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "#{hashtag} 팔로함"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "팔로잉…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "프로필에 표시되지 않음"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "프로필에 표시되지 않음 기능을 사용할 수 없습니다."

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "프로필에 내보임"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr ""

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "해시태그 더하기"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "해시태그 지우기"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr ""

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "이 바로 가기는 이미 있습니다"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "해시태그 바로 가기가 추가됨"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "바로 가기 추가"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "새 인스턴스 입력 (예: mastodon.social)"

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "잘못된 인스턴스"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "다른 인스턴스로 가기…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "내 인스턴스(<0>{currentInstance}</0>)로 가기"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "알림을 불러 올 수 없습니다."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>새</0> <1>팔로 요청</1>"

#: src/pages/home.jsx:251
msgid "See all"
msgstr "모두 보기"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "불러오는 중…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "URL을 불러올 수 없음"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "아직 아무 것도 없습니다."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "구성원 관리"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "<0>@{0}</0> 님을 리스트에서 뺄까요?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "지우기…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, other {리스트 #개}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "아직 아무 리스트도 없습니다."

#: src/pages/login.jsx:118
#: src/pages/login.jsx:128
msgid "Failed to register application"
msgstr "앱 등록에 실패함"

#: src/pages/login.jsx:214
msgid "instance domain"
msgstr "인스턴스 도메인"

#: src/pages/login.jsx:238
msgid "e.g. “mastodon.social”"
msgstr "예: “mastodon.social”"

#: src/pages/login.jsx:249
msgid "Failed to log in. Please try again or try another instance."
msgstr ""

#: src/pages/login.jsx:261
msgid "Continue with {selectedInstanceText}"
msgstr ""

#: src/pages/login.jsx:262
msgid "Continue"
msgstr "계속"

#: src/pages/login.jsx:270
msgid "Don't have an account? Create one!"
msgstr "계정이 없으신가요? 하나 만드세요!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "쪽지"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "쪽지"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "아무도 언급하지 않았습니다"

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "언급을 가져올 수 없습니다."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "팔로하지 않은 사람"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "날 팔로하지 않는 사람"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "새 계정으로"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr ""

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "모더레이터를 통해 제한된 유저"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "알림 설정"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "새 알림"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, other {공지}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "팔로 요청"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, other {팔로 요청 #건}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, other {#사람으로부터 필터된 알림}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "내 언급만"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "오늘"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "모두 따라잡았습니다."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "어제"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "알림을 가져올 수 없음"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "알림 설정이 바뀜"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "다음 사용자로부터 알림을 제외:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "필터"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "무시"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "<0>{0}</0> 업데이트됨"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "<0>@{0}</0> 로부터 받은 알림 보기"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "<0>@{0}</0> 로부터 받은 알림"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "<0>@{0}</0> 로부터 받은 알림은 이제부터 필터링되지 않습니다."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr ""

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "수락"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr ""

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "알림 요청을 해제할 수 없습니다."

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "닫기"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "닫힘"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "로컬 타임라인 ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "연합 타임라인 ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "로컬 타임라인"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "연합 타임라인"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "아직 아무도 게시물을 쓰지 않았습니다."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "연합으로 변경하기"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "로컬로 변경하기"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr ""

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr ""

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr ""

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr ""

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr ""

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr ""

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr ""

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr ""

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr ""

#: src/pages/search.jsx:50
msgid "Search: {q} (Posts)"
msgstr "검색: {q} (게시물)"

#: src/pages/search.jsx:53
msgid "Search: {q} (Accounts)"
msgstr "검색: {q} (계정)"

#: src/pages/search.jsx:56
msgid "Search: {q} (Hashtags)"
msgstr "검색: {q} (해시태그)"

#: src/pages/search.jsx:59
msgid "Search: {q}"
msgstr "검색: {q}"

#: src/pages/search.jsx:339
#: src/pages/search.jsx:421
msgid "Hashtags"
msgstr "해시태그"

#: src/pages/search.jsx:371
#: src/pages/search.jsx:425
#: src/pages/search.jsx:495
msgid "See more"
msgstr "더 보기"

#: src/pages/search.jsx:397
msgid "See more accounts"
msgstr "계정 더 보기"

#: src/pages/search.jsx:411
msgid "No accounts found."
msgstr "아무 계정도 찾을 수 없습니다."

#: src/pages/search.jsx:467
msgid "See more hashtags"
msgstr "해시태그 더 보기"

#: src/pages/search.jsx:481
msgid "No hashtags found."
msgstr "아무 해시태그도 찾을 수 없습니다."

#: src/pages/search.jsx:525
msgid "See more posts"
msgstr "게시물 더 보기"

#: src/pages/search.jsx:539
msgid "No posts found."
msgstr "아무 게시물도 찾을 수 없습니다."

#: src/pages/search.jsx:583
msgid "Enter your search term or paste a URL above to get started."
msgstr "시작하려면 위 검색창에 검색어를 입력하거나 URL을 붙여 넣으세요."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "설정"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "외관"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "밝게"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "어둡게"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "자동"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "글자 크기"

#. Preview of one character, in smallest size
#. Preview of one character, in largest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "글"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "표시 언어"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "번역 참여하기"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "게시"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "기본 공개 범위"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "동기화 됨"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "게시물 공개 범위 수정 실패"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "인스턴스 서버의 설정과 동기화 됩니다. <0>쓰고 있는 인스턴스({instance})에서 더 많은 설정이 가능합니다.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "시범 기능"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "타임라인 게시물 알아서 새로 고침"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "부스트 캐러셀"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "게시물 번역"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr ""

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "시스템 언어 ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, =0 {다음 언어에 대해 “번역” 버튼 가리기:} other {다음 #개 언어에 대해 “번역” 버튼 가리기:}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr ""

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "자동 번역"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "타임라인에서 게시물에 번역을 자동으로 보여줍니다. 열람 주의나 매체, 설문 조사가 없는 <0>짧은</0> 게시물에만 적용 됩니다."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "글쓰기 창에서 움짤 고르기"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "이 기능은 외부 움짤 검색 서비스인 <0>GIPHY</0>를 이용합니다. 전체관람가 움짤만 제공되며, 추적 매개변수는 제거되고 리퍼러 정보는 요청에서 생략되지만, 그럼에도 검색어와 IP 주소 정보는 해당 서비스에 전달 됩니다."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "이미지 설명 자동 생성기"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "새 게시물을 쓸 때 새로운 이미지에만 적용 됩니다."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "참고: 이 기능은 외부 인공지능 서비스인 <0>img-alt-api</0>를 이용합니다. 잘 동작하지 않을 수 있으며, 이미지에만 적용 가능하고 영어만 지원합니다."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "서버측에서 알림 묶기"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "알파 단계 기능입니다. 묶음의 크기가 커질 수도 있지만, 묶는 규칙은 기초적입니다."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "바로 가기 설정을 위해 \"클라우드\" 가져오기/내보내기"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr ""

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "알림: 이 기능은 현재 로그인한 인스턴스 서버 API를 사용합니다."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "가리기 모드 <0>(<1>글자들</1> → <2>███</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "글자를 모두 네모로 바꿔서 개인정보 염려 없이 스크린숏을 캡처할 수 있게 합니다."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "정보"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<1>@cheeaun</1>이 <0>만듦</0>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "후원자"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "기부"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr ""

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "개인 정보 보호 정책"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>사이트:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>버전:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "버전 번호 복사 됨"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "버전 번호를 복사할 수 없음"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "구독을 갱신하는 데 실패했습니다. 다시 시도해 보세요."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "구독을 삭제하는 데 실패했습니다. 다시 시도하세요."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "푸시 알림 (베타)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "푸시 알림이 차단되었습니다. 브라우저 설정에서 푸시 알림을 활성화하세요."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "<0>{0}</0>에게서 알림 받기"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "모두"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "내가 팔로하는 사람들"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "팔로워"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "팔로"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "설문 조사"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "게시물 수정"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "마지막 로그인 이후 푸시 권한이 부여되지 않았습니다. <0>푸시 권한을 다시 얻으려면<1>로그인</1>을</0>해야합니다."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "주의: 푸시 알림은 <0>단 하나의 계정</0>에만 작동합니다."

#. js-lingui-explicit-id
#: src/pages/status.jsx:657
#: src/pages/status.jsx:1220
msgid "post.title"
msgstr ""

#: src/pages/status.jsx:911
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "로그인하지 않았습니다. 상호작용(댓글, 부스트 등) 은 불가능합니다."

#: src/pages/status.jsx:931
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "이 게시글은 다른 인스턴스 (<0>{instance}</0>) 에서 작성돠었습니다. 상호작용(댓글, 부스트 등) 은 불가능합니다."

#: src/pages/status.jsx:959
msgid "Error: {e}"
msgstr "오류: {e}"

#: src/pages/status.jsx:966
msgid "Switch to my instance to enable interactions"
msgstr "내 인스턴스를 변경해 상호작용을 활성화시키기"

#: src/pages/status.jsx:1068
msgid "Unable to load replies."
msgstr "댓글을 불러 올 수 없습니다."

#: src/pages/status.jsx:1180
msgid "Back"
msgstr "뒤로"

#: src/pages/status.jsx:1211
msgid "Go to main post"
msgstr "원 게시물로 이동하기"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1234
msgid "{0} posts above ‒ Go to top"
msgstr ""

#: src/pages/status.jsx:1281
#: src/pages/status.jsx:1344
msgid "Switch to Side Peek view"
msgstr ""

#: src/pages/status.jsx:1345
msgid "Switch to Full view"
msgstr "전체 화면으로 전환"

#: src/pages/status.jsx:1363
msgid "Show all sensitive content"
msgstr "모든 민감한 내용 보기"

#: src/pages/status.jsx:1368
msgid "Experimental"
msgstr "시범적"

#: src/pages/status.jsx:1377
msgid "Unable to switch"
msgstr "전환할 수 없음"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1384
msgid "Switch to post's instance ({0})"
msgstr ""

#: src/pages/status.jsx:1387
msgid "Switch to post's instance"
msgstr "게시물의 인스턴스로 전환"

#: src/pages/status.jsx:1445
msgid "Unable to load post"
msgstr "게시물을 불러 올 수 없습니다"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1581
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, other {댓글 <0>{1}</0>개}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1599
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, other {댓글 <0>{0}</0>개}}"

#: src/pages/status.jsx:1621
msgid "View post with its replies"
msgstr "게시물과 댓글 보기"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "인기 ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "인기 뉴스"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr ""

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "인기 게시물 보기로 되돌아가기"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "<0>{0}</0> 님을 언급하는 게시물 보기"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "인기 게시물"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "인기 게시물이 없음."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "미니멀리즘을 추구하는 Mastodon 웹 클라이언트."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Mastodon으로 로그인"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "가입"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "기존 Mastodon/Fediverse 계정을 연결하세요.<0/>당신의 인증 수단은 이 서버에 저장되지 않습니다."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr ""

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "부스트 캐러셀의 스크린숏"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "부스트 캐러셀"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr ""

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr ""

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr ""

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr ""

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr ""

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "그룹 알림"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr ""

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "멀티 칼럼 UI 스크린샷"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "한 칼럼 혹은 멀티칼럼"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "기본적으로는 젠 모드를 추구하는 분들을 위해 한 칼럼으로 보입니다. 고급 사용자들을 위한 멀티 칼럼도 설정 가능합니다."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "해시태그를 더 추가할 수 있는 양식이 있는 멀티 해시태그 타임라인 스크린샷"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "다중 해시태그 타임라인"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "한 타임라인에 최대 5개 해시태그까지 지정할 수 있습니다."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "웹브라우저에서 팝업 윈도를 차단한 것 같습니다."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "게시글 초고가 현재 최소화되어있습니다. 새로운 게시글을 작성하기 전 올리거나 지우세요."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "게시글이 현재 열려있습니다. 새로운 게시글을 작성하기 전 올리거나 지우세요."

