msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: eu\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-06-20 01:13\n"
"Last-Translator: \n"
"Language-Team: Basque\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: eu\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:137
msgid "Locked"
msgstr "Babestua"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:143
msgid "Posts: {0}"
msgstr "Bidalketak: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:148
msgid "Last posted: {0}"
msgstr "Azken bidalketa: {0}"

#: src/components/account-block.jsx:163
#: src/components/account-info.jsx:710
msgid "Automated"
msgstr "Automatizatua"

#: src/components/account-block.jsx:170
#: src/components/account-info.jsx:715
#: src/components/status.jsx:590
msgid "Group"
msgstr "Taldea"

#: src/components/account-block.jsx:180
msgid "Mutual"
msgstr "Batak bestea"

#: src/components/account-block.jsx:184
#: src/components/account-info.jsx:1875
msgid "Requested"
msgstr "Eskatuta"

#: src/components/account-block.jsx:188
#: src/components/account-info.jsx:1866
msgid "Following"
msgstr "Jarraitzen"

#: src/components/account-block.jsx:192
#: src/components/account-info.jsx:1180
msgid "Follows you"
msgstr "Jarraitzen dizu"

#: src/components/account-block.jsx:200
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {jarraitzaile #} other {# jarraitzaile}}"

#: src/components/account-block.jsx:209
#: src/components/account-info.jsx:758
msgid "Verified"
msgstr "Egiaztatua"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:224
#: src/components/account-info.jsx:860
msgid "Joined <0>{0}</0>"
msgstr "<0>{0}</0>(e)an batu zen"

#: src/components/account-info.jsx:63
msgid "Forever"
msgstr "Betiko"

#: src/components/account-info.jsx:403
msgid "Unable to load account."
msgstr "Ezin da kontua kargatu."

#: src/components/account-info.jsx:418
msgid "Go to account page"
msgstr "Joan kontuaren orrira"

#: src/components/account-info.jsx:447
#: src/components/account-info.jsx:780
#: src/components/account-info.jsx:810
msgid "Followers"
msgstr "jarraitzaile"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:451
#: src/components/account-info.jsx:820
#: src/components/account-info.jsx:839
msgid "following.stats"
msgstr "jarraitzen"

#: src/components/account-info.jsx:454
#: src/components/account-info.jsx:856
#: src/pages/account-statuses.jsx:487
#: src/pages/search.jsx:344
#: src/pages/search.jsx:491
msgid "Posts"
msgstr "bidalketa"

#: src/components/account-info.jsx:462
#: src/components/account-info.jsx:1236
#: src/components/compose.jsx:2786
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1836
#: src/components/status.jsx:1853
#: src/components/status.jsx:1978
#: src/components/status.jsx:2599
#: src/components/status.jsx:2602
#: src/pages/account-statuses.jsx:531
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1305
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Gehiago"

#: src/components/account-info.jsx:474
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0>(e)k adierazi du kontu berria duela:"

#: src/components/account-info.jsx:619
#: src/components/account-info.jsx:1464
msgid "Handle copied"
msgstr "Helbidea kopiatu da"

#: src/components/account-info.jsx:622
#: src/components/account-info.jsx:1467
msgid "Unable to copy handle"
msgstr "Ezin da helbidea kopiatu"

#: src/components/account-info.jsx:628
#: src/components/account-info.jsx:1473
msgid "Copy handle"
msgstr "Kopiatu helbidea"

#: src/components/account-info.jsx:634
msgid "Go to original profile page"
msgstr "Joan jatorrizko profilaren orrira"

#: src/components/account-info.jsx:652
msgid "View profile image"
msgstr "Ikusi profileko irudia"

#: src/components/account-info.jsx:670
msgid "View profile header"
msgstr "Ikusi goiburuko irudia"

#: src/components/account-info.jsx:686
#: src/components/account-info.jsx:1774
#: src/components/account-info.jsx:2300
msgid "Edit profile"
msgstr "Editatu profila"

#: src/components/account-info.jsx:705
msgid "In Memoriam"
msgstr "In Memoriam"

#: src/components/account-info.jsx:787
#: src/components/account-info.jsx:830
msgid "This user has chosen to not make this information available."
msgstr "Erabiltzaileak informazio hau publiko ez egitea aukeratu du."

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:885
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} bidalketa original, {1} erantzun, {2} bultzada"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:901
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {Azken bidalketa azken egunean} other {Azken bidalketa azken {2} egunetan}}} other {{3, plural, one {Azken {4} bidalketak azken egunean} other {Azken {5} bidalketak azken {6} egunetan}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:917
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {Azken bidalketa azken urte(et)an} other {Azken {1} bidalketak azken urte(et)an}}"

#: src/components/account-info.jsx:942
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Originalak"

#: src/components/account-info.jsx:946
#: src/components/status.jsx:2383
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1024
#: src/pages/status.jsx:1651
msgid "Replies"
msgstr "Erantzunak"

#: src/components/account-info.jsx:950
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Bultzadak"

#: src/components/account-info.jsx:956
msgid "Post stats unavailable."
msgstr "Bidalketaren estatistikak ez daude erabilgarri."

#: src/components/account-info.jsx:987
msgid "View post stats"
msgstr "Ikusi bidalketen estatistikak"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1184
msgid "Last post: <0>{0}</0>"
msgstr "Azken bidalketa: <0>{0}</0>"

#: src/components/account-info.jsx:1198
msgid "Muted"
msgstr "Mutututa"

#: src/components/account-info.jsx:1203
msgid "Blocked"
msgstr "Blokeatuta"

#: src/components/account-info.jsx:1212
msgid "Private note"
msgstr "Ohar pribatua"

#: src/components/account-info.jsx:1269
msgid "Mention <0>@{username}</0>"
msgstr "Aipatu <0>@{username}</0>"

#: src/components/account-info.jsx:1281
msgid "Translate bio"
msgstr "Itzuli biografia"

#: src/components/account-info.jsx:1292
msgid "Edit private note"
msgstr "Editatu ohar pribatua"

#: src/components/account-info.jsx:1292
msgid "Add private note"
msgstr "Gehitu ohar pribatua"

#: src/components/account-info.jsx:1312
msgid "Notifications enabled for @{username}'s posts."
msgstr "Jakinarazpenak gaitu dira @{username}(r)en bidalketetarako."

#: src/components/account-info.jsx:1313
msgid " Notifications disabled for @{username}'s posts."
msgstr " Jakinarazpenak gaitu dira @{username}(r)en bidalketetarako."

#: src/components/account-info.jsx:1325
msgid "Disable notifications"
msgstr "Ezgaitu jakinarazpenak"

#: src/components/account-info.jsx:1326
msgid "Enable notifications"
msgstr "Gaitu jakinarazpenak"

#: src/components/account-info.jsx:1343
msgid "Boosts from @{username} enabled."
msgstr "@{username}(r)en bultzadak gaitu dira."

#: src/components/account-info.jsx:1344
msgid "Boosts from @{username} disabled."
msgstr "@{username}(r)en bultzadak ezgaitu dira."

#: src/components/account-info.jsx:1355
msgid "Disable boosts"
msgstr "Ezgaitu bultzadak"

#: src/components/account-info.jsx:1355
msgid "Enable boosts"
msgstr "Gaitu bultzadak"

#: src/components/account-info.jsx:1374
msgid "@{username} is no longer featured on your profile."
msgstr "@{username} zure profilean nabarmentzeari utzi zaio."

#: src/components/account-info.jsx:1384
msgid "@{username} is now featured on your profile."
msgstr "@{username} zure profilean nabarmenduta dago."

#: src/components/account-info.jsx:1392
msgid "Unable to unfeature @{username} on your profile."
msgstr "Ezin da @{username} zure profilean nabarmentzeari utzi."

#: src/components/account-info.jsx:1396
msgid "Unable to feature @{username} on your profile."
msgstr "Ezin da @{username} zure profilean nabarmendu."

#: src/components/account-info.jsx:1405
msgid "Don't feature on profile"
msgstr "Ez nabarmendu profilean"

#: src/components/account-info.jsx:1406
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Nabarmendu profilean"

#: src/components/account-info.jsx:1415
msgid "Show featured profiles"
msgstr "Erakutsi nabarmendutako profilak"

#: src/components/account-info.jsx:1430
#: src/components/account-info.jsx:1440
#: src/components/account-info.jsx:2046
msgid "Add/Remove from Lists"
msgstr "Gehitu zerrendara / kendu zerrendatik"

#: src/components/account-info.jsx:1490
#: src/components/status.jsx:1258
msgid "Link copied"
msgstr "Esteka kopiatu da"

#: src/components/account-info.jsx:1493
#: src/components/status.jsx:1261
msgid "Unable to copy link"
msgstr "Ezin da esteka kopiatu"

#: src/components/account-info.jsx:1499
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1267
#: src/components/status.jsx:3377
msgid "Copy"
msgstr "Kopiatu"

#: src/components/account-info.jsx:1514
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1283
msgid "Sharing doesn't seem to work."
msgstr "Ez dirudi partekatzea dabilenik."

#: src/components/account-info.jsx:1520
#: src/components/status.jsx:1289
msgid "Share…"
msgstr "Partekatu…"

#: src/components/account-info.jsx:1540
msgid "Unmuted @{username}"
msgstr "@{username} mututzeari utzi zaio"

#: src/components/account-info.jsx:1552
msgid "Unmute <0>@{username}</0>"
msgstr "Utzi <0>@{username}</0> mututzeari"

#: src/components/account-info.jsx:1568
msgid "Mute <0>@{username}</0>…"
msgstr "Mututu <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1600
msgid "Muted @{username} for {0}"
msgstr "@{username} {0}rako mututu da"

#: src/components/account-info.jsx:1612
msgid "Unable to mute @{username}"
msgstr "Ezin da @{username} mututu"

#: src/components/account-info.jsx:1633
msgid "Remove <0>@{username}</0> from followers?"
msgstr "<0>@{username}</0> jarraitzaileetatik kendu nahi?"

#: src/components/account-info.jsx:1653
msgid "@{username} removed from followers"
msgstr "@{username} jarraitzaileetatik kendu da"

#: src/components/account-info.jsx:1665
msgid "Remove follower…"
msgstr "Kendu jarraitzailea…"

#: src/components/account-info.jsx:1676
msgid "Block <0>@{username}</0>?"
msgstr "<0>@{username}</0> blokeatu nahi?"

#: src/components/account-info.jsx:1700
msgid "Unblocked @{username}"
msgstr "@{username} blokeatzeari utzi zaio"

#: src/components/account-info.jsx:1708
msgid "Blocked @{username}"
msgstr "@{username} blokeatu da"

#: src/components/account-info.jsx:1716
msgid "Unable to unblock @{username}"
msgstr "Ezin da @{username} blokeatzeari utzi"

#: src/components/account-info.jsx:1718
msgid "Unable to block @{username}"
msgstr "Ezin da @{username} blokeatu"

#: src/components/account-info.jsx:1728
msgid "Unblock <0>@{username}</0>"
msgstr "Utzi <0>@{username}</0> blokeatzeari"

#: src/components/account-info.jsx:1737
msgid "Block <0>@{username}</0>…"
msgstr "Blokeatu <0>@{username}</0>…"

#: src/components/account-info.jsx:1754
msgid "Report <0>@{username}</0>…"
msgstr "Salatu <0>@{username}</0>…"

#: src/components/account-info.jsx:1810
msgid "Withdraw follow request?"
msgstr "Jarraipen-eskaera atzera bota?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1811
msgid "Unfollow @{0}?"
msgstr "Utzi @{0} jarraitzeari?"

#: src/components/account-info.jsx:1869
msgid "Unfollow…"
msgstr "Utzi jarraitzeari…"

#: src/components/account-info.jsx:1878
msgid "Withdraw…"
msgstr "Bota atzera…"

#: src/components/account-info.jsx:1885
#: src/components/account-info.jsx:1889
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Jarraitu"

#: src/components/account-info.jsx:1986
#: src/components/account-info.jsx:2041
#: src/components/account-info.jsx:2175
#: src/components/account-info.jsx:2295
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:891
#: src/components/compose.jsx:2742
#: src/components/compose.jsx:3222
#: src/components/compose.jsx:3431
#: src/components/compose.jsx:3661
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:75
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3101
#: src/components/status.jsx:3341
#: src/components/status.jsx:3850
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1392
msgid "Close"
msgstr "Itxi"

#: src/components/account-info.jsx:1991
msgid "Translated Bio"
msgstr "Itzulitako biografia"

#: src/components/account-info.jsx:2086
msgid "Unable to remove from list."
msgstr "Ezin da zerrendatik kendu."

#: src/components/account-info.jsx:2087
msgid "Unable to add to list."
msgstr "Ezin da zerrendara gehitu."

#: src/components/account-info.jsx:2106
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Ezin dira zerrendak kargatu."

#: src/components/account-info.jsx:2110
msgid "No lists."
msgstr "Zerrendarik ez."

#: src/components/account-info.jsx:2121
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Zerrenda berria"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2180
msgid "Private note about <0>@{0}</0>"
msgstr "<0>@{0}</0>(r)i buruzko ohar pribatua"

#: src/components/account-info.jsx:2210
msgid "Unable to update private note."
msgstr "Ezin izan da ohar pribatua eguneratu."

#: src/components/account-info.jsx:2233
#: src/components/account-info.jsx:2531
msgid "Cancel"
msgstr "Utzi"

#: src/components/account-info.jsx:2238
msgid "Save & close"
msgstr "Gorde eta itxi"

#: src/components/account-info.jsx:2355
msgid "Unable to update profile."
msgstr "Ezin da profila eguneratu."

#: src/components/account-info.jsx:2362
msgid "Header picture"
msgstr "Goiburuko irudia"

#: src/components/account-info.jsx:2414
msgid "Profile picture"
msgstr "Profileko irudia"

#: src/components/account-info.jsx:2466
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Izena"

#: src/components/account-info.jsx:2479
msgid "Bio"
msgstr "Biografia"

#: src/components/account-info.jsx:2492
msgid "Extra fields"
msgstr "Eremu gehigarriak"

#: src/components/account-info.jsx:2498
msgid "Label"
msgstr "Etiketa"

#: src/components/account-info.jsx:2501
msgid "Content"
msgstr "Edukia"

#: src/components/account-info.jsx:2534
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Gorde"

#: src/components/account-info.jsx:2588
msgid "username"
msgstr "erabiltzaile-izena"

#: src/components/account-info.jsx:2592
msgid "server domain name"
msgstr "zerbitzariaren domeinu-izena"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2658
msgid "Profiles featured by @{0}"
msgstr "@{0}(e)k nabarmendutako profilak"

#: src/components/account-info.jsx:2684
msgid "No featured profiles."
msgstr "Ez dago nabarmendutako profilik."

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Estalki modua ezgaituta"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Estalki modua gaituta"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Hasiera"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Idatzi"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Programatutako bidalketak"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Gehitu harira"

#: src/components/compose.jsx:211
msgid "Take photo or video"
msgstr "Egin argazkia edo bideoa"

#: src/components/compose.jsx:212
msgid "Add media"
msgstr "Gehitu multimedia"

#: src/components/compose.jsx:213
msgid "Add custom emoji"
msgstr "Gehitu emoji pertsonala"

#: src/components/compose.jsx:214
msgid "Add GIF"
msgstr "Gehitu GIFa"

#: src/components/compose.jsx:215
msgid "Add poll"
msgstr "Gehitu bozketa"

#: src/components/compose.jsx:216
msgid "Schedule post"
msgstr "Programatu bidalketa"

#: src/components/compose.jsx:416
msgid "You have unsaved changes. Discard this post?"
msgstr "Gorde gabeko aldaketak dituzu. Bidalketa zokoratu nahi duzu?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:654
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {{1} fitxategia ez da bateragarria.} other {{2} fitxategiak ez dira bateragarriak.}}"

#: src/components/compose.jsx:664
#: src/components/compose.jsx:682
#: src/components/compose.jsx:1792
#: src/components/compose.jsx:1917
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Fitxategi bakarra erantsi dezakezu.} other {# fitxategi erantsi ditzakezu gehienez.}}"

#: src/components/compose.jsx:872
msgid "Pop out"
msgstr "Atera"

#: src/components/compose.jsx:879
msgid "Minimize"
msgstr "Ikonotu"

#: src/components/compose.jsx:915
msgid "Looks like you closed the parent window."
msgstr "Badirudi leiho nagusia itxi duzula."

#: src/components/compose.jsx:922
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Badirudi dagoeneko baduzula editorea irekita leiho nagusian eta zerbait argitaratzen ari zarela. Itxaron bukatu arte eta saiatu berriro geroago."

#: src/components/compose.jsx:927
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Badirudi dagoeneko baduzula editorea irekita leiho nagusian. Leiho berri bat ateraz gero, leiho nagusian egindako aldaketak zokoratuko dira. Jarraitu nahi duzu?"

#: src/components/compose.jsx:970
msgid "Pop in"
msgstr "Sartu"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:980
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "@{0}(r)en bidalketari erantzuten (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:990
msgid "Replying to @{0}’s post"
msgstr "@{0}(r)en bidalketari erantzuten"

#: src/components/compose.jsx:1003
msgid "Editing source post"
msgstr "Jatorrizko bidalketa editatzen"

#: src/components/compose.jsx:1056
msgid "Poll must have at least 2 options"
msgstr "Bozketak 2 aukera izan behar ditu gutxienez"

#: src/components/compose.jsx:1060
msgid "Some poll choices are empty"
msgstr "Bozketaren aukeretako batzuk hutsik daude"

#: src/components/compose.jsx:1073
msgid "Some media have no descriptions. Continue?"
msgstr "Ez dituzu multimedia fitxategi batzuk deskribatu. Jarraitu?"

#: src/components/compose.jsx:1125
msgid "Attachment #{i} failed"
msgstr "#{i} eranskinak huts egin du"

#: src/components/compose.jsx:1221
#: src/components/status.jsx:2166
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Edukiari buruzko abisua"

#: src/components/compose.jsx:1237
msgid "Content warning or sensitive media"
msgstr "Edukiari buruzko abisua edo multimedia hunkigarria"

#: src/components/compose.jsx:1273
#: src/components/status.jsx:96
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Publikoa"

#: src/components/compose.jsx:1278
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:97
msgid "Local"
msgstr "Lokala"

#: src/components/compose.jsx:1282
#: src/components/status.jsx:98
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Zerrendatu gabea"

#: src/components/compose.jsx:1285
#: src/components/status.jsx:99
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Jarraitzaileentzat soilik"

#: src/components/compose.jsx:1288
#: src/components/status.jsx:100
#: src/components/status.jsx:2042
msgid "Private mention"
msgstr "Aipamen pribatua"

#: src/components/compose.jsx:1297
msgid "Post your reply"
msgstr "Argitaratu erantzuna"

#: src/components/compose.jsx:1299
msgid "Edit your post"
msgstr "Editatu bidalketa"

#: src/components/compose.jsx:1300
msgid "What are you doing?"
msgstr "Zertan zabiltza?"

#: src/components/compose.jsx:1379
msgid "Mark media as sensitive"
msgstr "Markatu multimedia hunkigarri gisa"

#: src/components/compose.jsx:1416
msgid "Posting on <0/>"
msgstr "Programatu <0/>"

#: src/components/compose.jsx:1447
#: src/components/compose.jsx:3280
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Gehitu"

#: src/components/compose.jsx:1673
msgid "Schedule"
msgstr "Programatu"

#: src/components/compose.jsx:1675
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1030
#: src/components/status.jsx:1816
#: src/components/status.jsx:1817
#: src/components/status.jsx:2503
msgid "Reply"
msgstr "Erantzun"

#: src/components/compose.jsx:1677
msgid "Update"
msgstr "Eguneratu"

#: src/components/compose.jsx:1678
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Argitaratu"

#: src/components/compose.jsx:1804
msgid "Downloading GIF…"
msgstr "GIFa deskargatzen…"

#: src/components/compose.jsx:1832
msgid "Failed to download GIF"
msgstr "Ezin da GIFa deskargatu"

#: src/components/compose.jsx:2047
#: src/components/compose.jsx:2124
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Gehiago…"

#: src/components/compose.jsx:2556
msgid "Uploaded"
msgstr "Igota"

#: src/components/compose.jsx:2569
msgid "Image description"
msgstr "Irudiaren deskribapena"

#: src/components/compose.jsx:2570
msgid "Video description"
msgstr "Bideoaren deskribapena"

#: src/components/compose.jsx:2571
msgid "Audio description"
msgstr "Audioaren deskribapena"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2606
#: src/components/compose.jsx:2626
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Fitxategia handiegia da. Igoerak arazoak izan ditzake. Saiatu tamaina {0}tik {1} edo gutxiagora murrizten."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2618
#: src/components/compose.jsx:2638
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "Neurria handiegia da. Igoerak arazoak izan ditzake. Saiatu neurria {0}×{1}px-etik {2}×{3}px-era murrizten."

#: src/components/compose.jsx:2646
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Fotograma-tasa (frame rate) altuegia da. Igoerak arazoak izan ditzake."

#: src/components/compose.jsx:2706
#: src/components/compose.jsx:2956
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Kendu"

#: src/components/compose.jsx:2723
#: src/compose.jsx:84
msgid "Error"
msgstr "Errorea"

#: src/components/compose.jsx:2748
msgid "Edit image description"
msgstr "Editatu irudiaren deskribapena"

#: src/components/compose.jsx:2749
msgid "Edit video description"
msgstr "Editatu bideoaren deskribapena"

#: src/components/compose.jsx:2750
msgid "Edit audio description"
msgstr "Editatu audioaren deskribapena"

#: src/components/compose.jsx:2795
#: src/components/compose.jsx:2844
msgid "Generating description. Please wait…"
msgstr "Deskribapena sortzen. Itxaron…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2815
msgid "Failed to generate description: {0}"
msgstr "Ezin izan da deskribapena sortu: {0}"

#: src/components/compose.jsx:2816
msgid "Failed to generate description"
msgstr "Ezin izan da deskribapena sortu"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2834
#: src/components/compose.jsx:2880
msgid "Generate description…"
msgstr "Sortu deskribapena…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2867
msgid "Failed to generate description{0}"
msgstr "Ezin izan da deskribapena sortu{0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2882
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— esperimentala</0>"

#: src/components/compose.jsx:2901
msgid "Done"
msgstr "Eginda"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2937
msgid "Choice {0}"
msgstr "{0}. aukera"

#: src/components/compose.jsx:2984
msgid "Multiple choices"
msgstr "Hainbat aukera"

#: src/components/compose.jsx:2987
msgid "Duration"
msgstr "Iraupena"

#: src/components/compose.jsx:3018
msgid "Remove poll"
msgstr "Kendu bozketa"

#: src/components/compose.jsx:3239
msgid "Search accounts"
msgstr "Bilatu kontuak"

#: src/components/compose.jsx:3293
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Errorea kontuak kargatzean"

#: src/components/compose.jsx:3437
msgid "Custom emojis"
msgstr "Instantziako emojiak"

#: src/components/compose.jsx:3457
msgid "Search emoji"
msgstr "Bilatu emojia"

#: src/components/compose.jsx:3488
msgid "Error loading custom emojis"
msgstr "Errorea emoji pertsonalizatua kargatzean"

#: src/components/compose.jsx:3499
msgid "Recently used"
msgstr "Oraintsu erabilita"

#: src/components/compose.jsx:3500
msgid "Others"
msgstr "Besteak"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3538
msgid "{0} more…"
msgstr "{0} gehiago…"

#: src/components/compose.jsx:3676
msgid "Search GIFs"
msgstr "Bilatu GIFak"

#: src/components/compose.jsx:3691
msgid "Powered by GIPHY"
msgstr "GIPHYri esker"

#: src/components/compose.jsx:3699
msgid "Type to search GIFs"
msgstr "Idatzi GIFak bilatzeko"

#: src/components/compose.jsx:3797
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Aurrekoa"

#: src/components/compose.jsx:3815
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Hurrengoa"

#: src/components/compose.jsx:3832
msgid "Error loading GIFs"
msgstr "Errorea GIFak kargatzean"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Bidali gabeko zirriborroak"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Badirudi bidali gabeko zirriborroak dituzula. Jarrai ezazu utzi zenuen tokian."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Zirriborroa ezabatu nahi duzu?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Errorea zirriborroa ezabatzean! Saiatu berriro."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1433
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Ezabatu…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Errorea erantzuten ari zaion egoera eskuratzean!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Zirriborro guztiak ezabatu?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Errorea zirriborroak ezabatzean! Saiatu berriro."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Ezabatu guztia…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Ez dago zirriborrorik."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Bozketa"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:367
msgid "Media"
msgstr "Multimedia"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Ireki leiho berrian"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Onartu"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Ukatu"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Onartuta"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Ukatuta"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:334
#: src/pages/search.jsx:367
msgid "Accounts"
msgstr "Kontuak"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:561
#: src/pages/status.jsx:1425
msgid "Show more…"
msgstr "Erakutsi gehiago…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:566
msgid "The end."
msgstr "Amaiera."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Ezertxo ere ez"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Laster-teklak"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Teklatuko laster-teklen laguntza"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Hurrengo bidalketa"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Aurreko bidalketa"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Biratu karrusela hurrengora"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Shift</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Biratu karrusela aurrekora"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Shift</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Kargatu bidalketa berriak"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Ireki bidalketaren xehetasunak"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> or <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Hedatu edukiaren abisua edo<0/>hedatu / tolestu haria"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Itxi bidalketa edo leihoa"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> or <1>Backspace</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Fokatu zutabea zutabe anitzeko antolaketan"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> to <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Fokatu hurrengo zutabean zutabe anitzeko antolaketan"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Fokatu aurreko zutabean zutabe anitzeko antolaketan"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Idatzi bidalketa berria"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Idatzi bidalketa berria (leiho berria)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Shift</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Bidali"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:73
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:46
#: src/pages/search.jsx:316
msgid "Search"
msgstr "Bilatu"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Erantzun (leiho berria)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Shift</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Egin gogoko"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> edo <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1038
#: src/components/status.jsx:2530
#: src/components/status.jsx:2553
#: src/components/status.jsx:2554
msgid "Boost"
msgstr "Bultzatu"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Shift</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1101
#: src/components/status.jsx:2578
#: src/components/status.jsx:2579
msgid "Bookmark"
msgstr "Jarri laster-marka"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Estalki modua bai/ez"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Shift</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Editatu zerrenda"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Ezin da zerrenda editatu."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Ezin da zerrenda sortu."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Erakutsi zerrandako kideen erantzunak"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Erakutsi erantzunak jarraitzen diedan pertsonei"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Ez erakutsi erantzunik"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Ezkutatu Hasiera / Jarraitzen -etik zerrenda honetako bidalketak"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Sortu"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Zerrenda ezabatu nahi duzu?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Ezin da zerrenda ezabatu."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "Zerrenda honetako bidalketak ez dira Hasiera / Jarraitzen denbora-lerroan erakutsiko"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Fitxategiaren deskribapena"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1144
#: src/components/status.jsx:1153
#: src/components/translation-block.jsx:237
msgid "Translate"
msgstr "Itzuli"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1172
msgid "Speak"
msgstr "Irakurri ozenki"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Ireki jatorrizko multimedia fitxategia leiho berrian"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Ireki jatorrizko multimedia fitxategia"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Irudia deskribatzen saiatzen. Itxaron…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Ezin da irudia deskribatu"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Deskribatu irudia…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Ikusi bidalketa"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Multimedia fitxategi hunkigarria"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Iragazita: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3680
#: src/components/status.jsx:3776
#: src/components/status.jsx:3854
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Iragazita"

#: src/components/media.jsx:469
msgid "Open file"
msgstr "Ireki fitxategia"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Programatutako bidalketa"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Bidalketa argitaratu da. Ikus ezazu."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Programatutako erantzuna"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Erantzuna argitaratu da. Ikus ezazu."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Bidalketa eguneratu da. Ikus ezazu."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Menua"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Birkargatu orria orain eguneratzeko?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Eguneraketa eskuragarri…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "Jarraitzen ditu"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Zer berri?"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Aipamenak"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Jakinarazpenak"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Berria"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Profila"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Laster-markak"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Gogokoak"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Jarraitutako traolak"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:333
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Iragazkiak"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Mutututako erabiltzaileak"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Mutututakoak…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Blokeatutako erabiltzaileak"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Blokeatutakoak…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Kontuak…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:195
#: src/pages/status.jsx:924
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Hasi saioa"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Joerak"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Federatua"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Lasterbideak / Zutabeak…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Ezarpenak…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Zerrendak"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Zerrenda guztiak"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Jakinarazpenak"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Jakinarazpena zure beste kontuarena da."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Ikusi jakinarazpen guztiak"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account}(e)k zure bidalketari {emojiObject}rekin erantzun dio"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account}(e)k bidalketa bat argitaratu du"

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account}(e)k zure erantzuna bultzatu du.} other {{account}(e)k zure bidalketa bultzatu du.}}} other {{account}(e)k zure {postsCount} bidalketa bultzatu ditu.}}} other {{postType, select, reply {<0><1>{0}</1> pertsonak</0> zure erantzuna bultzatu dute.} other {<2><3>{1}</3> pertsonak</2> zure bidalketa bultzatu dute.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, one {}=1 {{account}(e)k jarraitu dizu.} other {<0><1>{0}</1> pertsonak</0> jarraitu dizute.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account}(e)k jarraitzeko eskaera egin dizu."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account}(e)k zure erantzuna gogoko du.} other {{account}(e)k zure bidalketa gogoko du.}}} other {{account}(e)k zure {postsCount} bidalketa gogoko ditu.}}} other {{postType, select, reply {<0><1>{0}</1> pertsonak</0> zure erantzuna gogoko dute.} other {<2><3>{1}</3> pertsonak</2> zure bidalketa gogoko dute.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Sortu edo boza eman zenuen bozketa bat amaitu da."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Sortu zenuen bozketa bat amaitu da."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Boza eman zenuen bozketa bat amaitu da."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Interaktuatu zenuen bidalketa bat editatu dute."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account}(e)ek zure erantzuna gogoko egin eta bultzatu du.} other {{account}(e)ek zure bidalketa gogoko egin eta bultzatu du.}}} other {{account}(e)k (e)ek zure  {postsCount} bidalketa gogoko egin eta bultzatu ditu.}}} other {{postType, select, reply {<0><1>{0}</1> pertsonak</0> erantzuna gogoko egin eta bultzatu dute.} other {<2><3>{1}</3> pertsonak</2> zure bidalketa gogoko egin eta bultzatu dute.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account}(e)k izena eman du."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account}(e)k {targetAccount} salatu du"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "<0>{name}</0>(r)ekin harremana galdu da."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Moderazio-ohartarazpena"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Hemen da {year}ko #Wrapstodon!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "<0>{from}</0>(e)ko administratzaile batek <1>{targetName}</1> bertan behera utzi du, eta horrek esan nahi du aurrerantzean ezingo duzula harekin harremanik izan."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "<0>{from}</0>(e)ko administratzaile batek <1>{targetName}</1> blokeatu du. Eragina du jarraitzen dizuten {followersCount} eta jarraitzen dituzun {followingCount} -engan."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "<0>{targetName}</0> blokeatu duzu. Jarraitzen zizuten {followersCount} eta jarraitzen zenituen {followingCount} kendu dira."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Zure kontuak moderazio-ohartarazpen bat jaso du."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Zure kontua ezgaitu da."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Zure bidalketa batzuk hunkigarri gisa markatu dira."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Zure bidalketa batzuk ezabatu dira."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "Aurrerantzean zure bidalketak hunkigarri gisa markatuko dira."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Zure kontuari mugak jarri zaizkio."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Zure kontua bertan behera utzi da."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Jakinarazpen mota ezezaguna: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1115
#: src/components/status.jsx:1125
msgid "Boosted/Liked by…"
msgstr "Bultzatu/Gogoko du(te)…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Gogoko du…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Bultzatu du…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Jarraitzen dizu…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Ikasi gehiago <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Ikusi #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:328
msgid "Read more →"
msgstr "Irakurri gehiago →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Bozkatu duzu"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {boto #} other {# boto}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Ezkutatu emaitzak"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Bozkatu"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1294
#: src/pages/status.jsx:1317
msgid "Refresh"
msgstr "Freskatu"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Erakutsi emaitzak"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {boto <0>{0}</0>} other {<1>{1}</1> boto}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {hautesle <0>{0}</0>} other {<1>{1}</1> hautesle}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "<0/> amaitu da"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Amaitu da"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "<0/> amaituko da"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Amaiera-data"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}m"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}h"

#: src/components/report-modal.jsx:29
msgid "Spam"
msgstr "Mezu baztergarria"

#: src/components/report-modal.jsx:30
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Esteka maltzurrak, gezurrezko elkarrekintzak edo erantzun errepikakorrak"

#: src/components/report-modal.jsx:33
msgid "Illegal"
msgstr "Legez kanpokoa"

#: src/components/report-modal.jsx:34
msgid "Violates the law of your or the server's country"
msgstr "Zure edo zerbitzariaren herrialdeko legedia urratzen du"

#: src/components/report-modal.jsx:37
msgid "Server rule violation"
msgstr "Zerbitzariaren arauen urraketa"

#: src/components/report-modal.jsx:38
msgid "Breaks specific server rules"
msgstr "Zerbitzariaren arau jakinak urratzen ditu"

#: src/components/report-modal.jsx:39
msgid "Violation"
msgstr "Urraketa"

#: src/components/report-modal.jsx:42
msgid "Other"
msgstr "Beste zerbait"

#: src/components/report-modal.jsx:43
msgid "Issue doesn't fit other categories"
msgstr "Arazoa ez dator bat beste kategoriekin"

#: src/components/report-modal.jsx:68
msgid "Report Post"
msgstr "Salatu bidalketa"

#: src/components/report-modal.jsx:68
msgid "Report @{username}"
msgstr "Salatu @{username}"

#: src/components/report-modal.jsx:104
msgid "Pending review"
msgstr "Berrikusketeke"

#: src/components/report-modal.jsx:146
msgid "Post reported"
msgstr "Bidalketa salatu da"

#: src/components/report-modal.jsx:146
msgid "Profile reported"
msgstr "Profila salatu da"

#: src/components/report-modal.jsx:154
msgid "Unable to report post"
msgstr "Ezin da bidalketa salatu"

#: src/components/report-modal.jsx:155
msgid "Unable to report profile"
msgstr "Ezin da profila salatu"

#: src/components/report-modal.jsx:163
msgid "What's the issue with this post?"
msgstr "Zein da bidalketaren arazoa?"

#: src/components/report-modal.jsx:164
msgid "What's the issue with this profile?"
msgstr "Zein da profilaren arazoa?"

#: src/components/report-modal.jsx:233
msgid "Additional info"
msgstr "Informazio gehigarria"

#: src/components/report-modal.jsx:256
msgid "Forward to <0>{domain}</0>"
msgstr "Birbidali <0>{domain}</0>(e)ra"

#: src/components/report-modal.jsx:266
msgid "Send Report"
msgstr "Bidali salaketa"

#: src/components/report-modal.jsx:275
msgid "Muted {username}"
msgstr "{username} mutututa"

#: src/components/report-modal.jsx:278
msgid "Unable to mute {username}"
msgstr "Ezin da {username} mututu"

#: src/components/report-modal.jsx:283
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Bidali salaketa <0>+ Mututu profila</0>"

#: src/components/report-modal.jsx:294
msgid "Blocked {username}"
msgstr "{username} blokeatuta"

#: src/components/report-modal.jsx:297
msgid "Unable to block {username}"
msgstr "Ezin da {username} blokeatu"

#: src/components/report-modal.jsx:302
msgid "Send Report <0>+ Block profile</0>"
msgstr "Bidali salaketa <0>+ Blokeatu profila</0>"

#: src/components/search-form.jsx:203
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ kontuak, traolak eta bidalketak</0>"

#: src/components/search-form.jsx:216
msgid "Posts with <0>{query}</0>"
msgstr "<0>{query}</0> duten bidalketak"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:228
msgid "Posts tagged with <0>#{0}</0>"
msgstr "<0>#{0}</0>(r)ekin etiketatutako bidalketak"

#: src/components/search-form.jsx:242
msgid "Look up <0>{query}</0>"
msgstr "Bilatu <0>{query}</0>"

#: src/components/search-form.jsx:253
msgid "Accounts with <0>{query}</0>"
msgstr "<0>{query}</0> duten kontuak"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Hasiera / Jarraitzen"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Publikoa (Lokala / Federatua)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Kontua"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Traolak"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "Zerrendaren IDa"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Lokala bakarrik"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:199
msgid "Instance"
msgstr "Instantzia"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Aukerakoa, adib. mastodon.eus"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Bilatzeko terminoa"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Aukerakoa, zutabe anitzeko antolaketa erabili ezean"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "adib. PixelArt (5 gehienez, espazioen bidez bereizita)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Multimedia bakarrik"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Lasterbideak"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "beta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Zehaztu lasterbideen zerrenda bat honela agertzeko:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Botoi mugikorra"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Erlaitz- / Menu-barra"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Zutabe anitz"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Ez dago erabilgarri uneko ikuspegian"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Mugitu gora"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Mugitu behera"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1395
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Editatu"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Gehitu lasterbide / zutabe bat baino gehiago erabili ahal izateko."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Oraindik ez dago zutaberik. Egin tap Gehitu zutabea botoian."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Oraindik ez dago lasterbiderik. Egin tap Gehitu lasterbidea botoian."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Ez dakizu zer gehitu?<0/>Probatu <1>Hasiera / Jarraitzen eta Jakinarazpenak</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "{SHORTCUTS_LIMIT} zutabe gehienez"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "{SHORTCUTS_LIMIT} lasterbide gehienez"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Inportatu / esportatu"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Gehitu zutabea…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Gehitu lasterbidea…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Zerrenda zehatza aukerakoa da. Zutabe anitzeko antolaketan zerrenda beharrezkoa da, bestela zutabea ez da erakutsiko."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Zutabe anitzeko antolaketan bilatzeko terminoa beharrezkoa da, bestela zutabea ez da erakutsiko."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Traola bat baino gehiago onartzen dira. Espazioen bidez bereizita."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Editatu lasterbidea"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Gehitu lasterbidea"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Denbora-lerroa"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Zerrenda"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Inportatu / Esportatu <0>Lasterbideak</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Inportatu"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Itsatsi lasterbideak hemen"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Gordetako lasterbideak zerbitzaritik deskargatzen…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Ezin dira lasterbideak deskargatu"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Deskargatu lasterbideak zerbitzaritik"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Lasterbidea badago lehendik ere"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "Litekeena da zerrendak ezin erabiltzea beste kontu batekoak badira."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Ezarpenen formatu baliogabea"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Gehitu uneko lasterbideei?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Uneko lasterbideetan ez daudenak gehituko dira soilik."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Ez dago inportatzeko lasterbide berririk"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Lasterbideak inportatu dira. {SHORTCUTS_LIMIT} muga gainditu da, gainerakoak ez dira inportatuko."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Lasterbideak inportatu dira"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Inportatu eta gehitu…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Uneko lasterbideak gainidatzi?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Lasterbideak inportatu?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "edo gainidatzi…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Inportatu…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Esportatu"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Lasterbideak kopiatu dira"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Ezin dira lasterbideak kopiatu"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Lasterbideen ezarpenak kopiatu dira"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Ezin dira lasterbideen ezarpenak kopiatu"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Partekatu"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Lasterbideak zerbitzarian gordetzen…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Lasterbideak gorde dira"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Ezin dira lasterbideak gorde"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Sinkronizatu zerbitzariarekin"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {karaktere #} other {# karaktere}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Lasterbideen JSON gordina"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Inportatu / Esportatu ezarpenak zerbitzarira / zerbitzaritik (oso esperimentala)"

#: src/components/status.jsx:614
msgid "<0/> <1>boosted</1>"
msgstr "<0/>(e)k <1>bultzatua</1>"

#: src/components/status.jsx:713
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Barka baina saioa hasita duzun zerbitzariak ezin du interaktuatu beste instantzia batekoa den bidalketa honekin."

#. placeholder {0}: username || acct
#: src/components/status.jsx:867
msgid "Unliked @{0}'s post"
msgstr "@{0}(r)en bidalketa gogoko izateari utzi diozu"

#. placeholder {0}: username || acct
#: src/components/status.jsx:868
msgid "Liked @{0}'s post"
msgstr "@{0}(r)en bidalketa gogoko egin du"

#. placeholder {0}: username || acct
#: src/components/status.jsx:907
msgid "Unbookmarked @{0}'s post"
msgstr "@{0}(r)en bidalketari laster-marka kendu dio"

#. placeholder {0}: username || acct
#: src/components/status.jsx:908
msgid "Bookmarked @{0}'s post"
msgstr "@{0}(r)en bidalketari laster-marka jarri dio"

#: src/components/status.jsx:1007
msgid "Some media have no descriptions."
msgstr "Multimedia fitxategi batzuek ez dute deskribapenik."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1014
msgid "Old post (<0>{0}</0>)"
msgstr "Bidalketa zaharra (<0>{0}</0>)"

#: src/components/status.jsx:1038
#: src/components/status.jsx:1078
#: src/components/status.jsx:2530
#: src/components/status.jsx:2553
msgid "Unboost"
msgstr "Kendu bultzada"

#: src/components/status.jsx:1054
#: src/components/status.jsx:2545
msgid "Quote"
msgstr "Aipatu"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1066
#: src/components/status.jsx:1532
msgid "Unboosted @{0}'s post"
msgstr "@{0}(r)en bidalketari bultzada kendu dio"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1067
#: src/components/status.jsx:1533
msgid "Boosted @{0}'s post"
msgstr "@{0}(r)en bidalketa bultzatu du"

#: src/components/status.jsx:1079
msgid "Boost…"
msgstr "Bultzatu…"

#: src/components/status.jsx:1091
#: src/components/status.jsx:1826
#: src/components/status.jsx:2566
msgid "Unlike"
msgstr "Utzi gogoko egiteari"

#: src/components/status.jsx:1092
#: src/components/status.jsx:1826
#: src/components/status.jsx:1827
#: src/components/status.jsx:2566
#: src/components/status.jsx:2567
msgid "Like"
msgstr "Gogoko egin"

#: src/components/status.jsx:1101
#: src/components/status.jsx:2578
msgid "Unbookmark"
msgstr "Kendu laster-marka"

#: src/components/status.jsx:1184
msgid "Post text copied"
msgstr "Bidalketako testua kopiatu da"

#: src/components/status.jsx:1187
msgid "Unable to copy post text"
msgstr "Ezin da bidalketako testua kopiatu"

#: src/components/status.jsx:1193
msgid "Copy post text"
msgstr "Kopiatu bidalketako testua"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1211
msgid "View post by <0>@{0}</0>"
msgstr "Ikusi <0>@{0}</0>(r)en bidalketa"

#: src/components/status.jsx:1232
msgid "Show Edit History"
msgstr "Erakutsi edizio-historia"

#: src/components/status.jsx:1235
msgid "Edited: {editedDateText}"
msgstr "Editatuta: {editedDateText}"

#: src/components/status.jsx:1302
#: src/components/status.jsx:3346
msgid "Embed post"
msgstr "Txertatu bidalketa"

#: src/components/status.jsx:1316
msgid "Conversation unmuted"
msgstr "Elkarrizketa mututzeari utzi zaio"

#: src/components/status.jsx:1316
msgid "Conversation muted"
msgstr "Elkarrizketa mututu da"

#: src/components/status.jsx:1322
msgid "Unable to unmute conversation"
msgstr "Ezin da elkarrizketa mututzeari utzi"

#: src/components/status.jsx:1323
msgid "Unable to mute conversation"
msgstr "Ezin da elkarrizketa mututu"

#: src/components/status.jsx:1332
msgid "Unmute conversation"
msgstr "Utzi elkarrizketa mututzeari"

#: src/components/status.jsx:1339
msgid "Mute conversation"
msgstr "Mututu elkarrizketa"

#: src/components/status.jsx:1355
msgid "Post unpinned from profile"
msgstr "Bidalketa profilean finkatzeari utzi zaio"

#: src/components/status.jsx:1356
msgid "Post pinned to profile"
msgstr "Profilean finkatutako bidalketa"

#: src/components/status.jsx:1361
msgid "Unable to unpin post"
msgstr "Ezin da bidalketa finkatzeari utzi"

#: src/components/status.jsx:1361
msgid "Unable to pin post"
msgstr "Ezin da bidalketa finkatu"

#: src/components/status.jsx:1370
msgid "Unpin from profile"
msgstr "Utzi profilean finkatzeari"

#: src/components/status.jsx:1377
msgid "Pin to profile"
msgstr "Finkatu profilean"

#: src/components/status.jsx:1406
msgid "Delete this post?"
msgstr "Bidalketa ezabatu nahi duzu?"

#: src/components/status.jsx:1422
msgid "Post deleted"
msgstr "Bidalketa ezabatu da"

#: src/components/status.jsx:1425
msgid "Unable to delete post"
msgstr "Ezin da bidalketa ezabatu"

#: src/components/status.jsx:1453
msgid "Report post…"
msgstr "Salatu bidalketa…"

#: src/components/status.jsx:1827
#: src/components/status.jsx:1863
#: src/components/status.jsx:2567
msgid "Liked"
msgstr "Gogoko egina"

#: src/components/status.jsx:1860
#: src/components/status.jsx:2554
msgid "Boosted"
msgstr "Bultzatua"

#: src/components/status.jsx:1870
#: src/components/status.jsx:2579
msgid "Bookmarked"
msgstr "Laster-marka jarria"

#: src/components/status.jsx:1874
msgid "Pinned"
msgstr "Finkatua"

#: src/components/status.jsx:1920
#: src/components/status.jsx:2391
msgid "Deleted"
msgstr "Ezabatua"

#: src/components/status.jsx:1961
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {erantzun #} other {# erantzun}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2051
msgid "Thread{0}"
msgstr "Haria{0}"

#: src/components/status.jsx:2129
#: src/components/status.jsx:2191
#: src/components/status.jsx:2287
msgid "Show less"
msgstr "Ezkutatu edukia"

#: src/components/status.jsx:2129
#: src/components/status.jsx:2191
msgid "Show content"
msgstr "Erakutsi edukia"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2283
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Iragazita: {0}"

#: src/components/status.jsx:2287
msgid "Show media"
msgstr "Erakutsi multimedia fitxategiak"

#: src/components/status.jsx:2427
msgid "Edited"
msgstr "Editatuta"

#: src/components/status.jsx:2504
msgid "Comments"
msgstr "Iruzkinak"

#. More from [Author]
#: src/components/status.jsx:2804
msgid "More from <0/>"
msgstr "<0/>(r)en gehiago"

#: src/components/status.jsx:3106
msgid "Edit History"
msgstr "Edizio-historia"

#: src/components/status.jsx:3110
msgid "Failed to load history"
msgstr "Ezin da historia kargatu"

#: src/components/status.jsx:3115
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Kargatzen…"

#: src/components/status.jsx:3351
msgid "HTML Code"
msgstr "HTML kodea"

#: src/components/status.jsx:3368
msgid "HTML code copied"
msgstr "HTML kodea kopiatu da"

#: src/components/status.jsx:3371
msgid "Unable to copy HTML code"
msgstr "Ezin da HTML kodea kopiatu"

#: src/components/status.jsx:3383
msgid "Media attachments:"
msgstr "Multimedia eranskinak:"

#: src/components/status.jsx:3405
msgid "Account Emojis:"
msgstr "Kontuaren emojiak:"

#: src/components/status.jsx:3436
#: src/components/status.jsx:3481
msgid "static URL"
msgstr "URL estatikoa"

#: src/components/status.jsx:3450
msgid "Emojis:"
msgstr "Emojiak:"

#: src/components/status.jsx:3495
msgid "Notes:"
msgstr "Oharrak:"

#: src/components/status.jsx:3499
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Hau estatikoa da, diseinurik eta scriptik gabekoa. Litekeena da zure estiloak aplikatu eta editatu behar izatea."

#: src/components/status.jsx:3505
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Bozketak ez dira interaktiboak, zerrendak bilakatzen dira boto-zenbaketan."

#: src/components/status.jsx:3510
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Multimedia eranskinak irudiak, bideoak, audioak edo edozein fitxategi mota izan daitezke."

#: src/components/status.jsx:3516
msgid "Post could be edited or deleted later."
msgstr "Bidalketa editatu edo ezabatu daiteke geroago."

#: src/components/status.jsx:3522
msgid "Preview"
msgstr "Aurrebista"

#: src/components/status.jsx:3531
msgid "Note: This preview is lightly styled."
msgstr "Oharra: aurrebista honi estilo arin bat aplikatu zaio."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3784
msgid "<0/> <1/> boosted"
msgstr "<0/>(e)k <1/> bultzatu du"

#: src/components/status.jsx:3897
msgid "Post removed by author."
msgstr ""

#: src/components/status.jsx:3906
msgid "You’re not authorized to view this post."
msgstr ""

#: src/components/status.jsx:3915
msgid "Post pending author approval."
msgstr ""

#: src/components/status.jsx:3924
msgid "Quoting not allowed by the author."
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Bidalketa berriak"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1077
#: src/pages/status.jsx:1454
msgid "Try again"
msgstr "Saiatu berriro"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {Bultzada #} other {# bultzada}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Finkatutako bidalketak"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Haria"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Iragazita</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:194
msgid "Auto-translated from {sourceLangText}"
msgstr "{sourceLangText} automatikoki itzultzen"

#: src/components/translation-block.jsx:232
msgid "Translating…"
msgstr "Itzultzen…"

#: src/components/translation-block.jsx:235
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Itzuli {sourceLangText} (automatikoki antzemanda)"

#: src/components/translation-block.jsx:236
msgid "Translate from {sourceLangText}"
msgstr "Itzuli {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:264
msgid "Auto ({0})"
msgstr "Automatikoa ({0})"

#: src/components/translation-block.jsx:277
msgid "Failed to translate"
msgstr "Ezin da itzuli"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Jatorrizko egoera editatzen"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "@{0}(r)i erantzuten"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Orria itxi dezakezu."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Itxi leihoa"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Saioa hasi behar da."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:276
msgid "Go home"
msgstr "Joan orri nagusira"

#: src/pages/account-statuses.jsx:238
msgid "Account posts"
msgstr "Kontuaren bidalketak"

#: src/pages/account-statuses.jsx:245
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Erantzunak)"

#: src/pages/account-statuses.jsx:247
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Bultzadak)"

#: src/pages/account-statuses.jsx:249
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:251
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Multimedia)"

#: src/pages/account-statuses.jsx:257
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:323
msgid "Clear filters"
msgstr "Garbitu iragazkiak"

#: src/pages/account-statuses.jsx:326
msgid "Clear"
msgstr "Garbitu"

#: src/pages/account-statuses.jsx:340
msgid "Showing post with replies"
msgstr "Erantzunak dituzten bidalketak erakusten"

#: src/pages/account-statuses.jsx:345
msgid "+ Replies"
msgstr "+ Erantzunak"

#: src/pages/account-statuses.jsx:351
msgid "Showing posts without boosts"
msgstr "Bultzadarik ez duten bidalketak erakusten"

#: src/pages/account-statuses.jsx:356
msgid "- Boosts"
msgstr "- Bultzadak"

#: src/pages/account-statuses.jsx:362
msgid "Showing posts with media"
msgstr "Multimedia fitxategiak dituzten bidalketak erakusten"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:379
msgid "Showing posts tagged with #{0}"
msgstr "#{0} traola duten bidalketak erakusten"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:418
msgid "Showing posts in {0}"
msgstr "{0}(e)ko bidalketak erakusten"

#: src/pages/account-statuses.jsx:508
msgid "Nothing to see here yet."
msgstr "Ez dago ikusteko ezer."

#: src/pages/account-statuses.jsx:509
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Ezin dira bidalketak kargatu"

#: src/pages/account-statuses.jsx:550
#: src/pages/account-statuses.jsx:580
msgid "Unable to fetch account info"
msgstr "Ezin da kontuaren informazioa eskuratu"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:557
msgid "Switch to account's instance {0}"
msgstr "Aldatu kontuko instantziara {0}"

#: src/pages/account-statuses.jsx:587
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Aldatu nire instantziara (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:660
msgid "Month"
msgstr "Hilabetea"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Unean"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Lehenetsia"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Aldatu kontu honetara"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Aldatu fitxa / leiho berrian"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Ikusi profila…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Ezarri lehenetsi gisa"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "<0>@{0}</0> saioa amaitu nahi?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Amaitu saioa…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr "{0}(e)ra konektatuta (<0/>)"

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Gehitu lehendik dudan kontu bat"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Oharra: <0>Lehenetsitako</0> kontua erabiliko da beti hasieran. Gainerako kontuek saioa irekita mantendu bitartean iraungo dute."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Ez duzu laster-markarik. Zoaz gordetzea merezi duen zerbait aurkitzera!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Ezin dira laster-markak kargatu."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "azken orduko bidalketak"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "azken 2 ordutako bidalketak"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "azken 3 ordutako bidalketak"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "azken 4 ordutako bidalketak"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "azken 5 ordutako bidalketak"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "azken 6 ordutako bidalketak"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "azken 7 ordutako bidalketak"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "azken 8 ordutako bidalketak"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "azken 9 ordutako bidalketak"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "azken 10 ordutako bidalketak"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "azken 11 ordutako bidalketak"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "azken 12 ordutako bidalketak"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "12 ordu baino haratagoko bidalketak"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Jarraitutako traolak"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Taldeak"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "{selectedFilterCategory, select, all {Bidalketa guztiak} original {Bidalketa originalak} replies {Erantzunak} boosts {Bultzadak} followedTags {Jarraitutako traolak} groups {Taldeak} filtered {Iragazitako bidalketak}} erakusten, {sortBy, select, createdAt {{sortOrder, select, asc {zaharrena} desc {berriena}}} reblogsCount {{sortOrder, select, asc {bultzada gutxien dituena} desc {bultzada gehien dituena}}} favouritesCount {{sortOrder, select, asc {gogoko gutxien dituena} desc {gogoko gehien dituena}}} repliesCount {{sortOrder, select, asc {erantzun gutxien dituena} desc {erantzun gehien dituena}}} density {{sortOrder, select, asc {dentsitate txikiena duena} desc {dentsitate handiena duena}}}}ren arabera sailkatuta lehenengo{groupBy, select, account {, egilearen arabera aldekatuta} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Zer berri? <0>beta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Laguntza"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Zer da hau?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Zer berri? denbora-lerro bereizi bat da jarraitzen dituzunekin; goi-mailako ikuspegi bat eskaintzen du begirada batean, posta elektronikoan oinarritutako interfaze erraz batekin, mezuak ordenatzea eta iragaztea ahalbidetzen duena."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Zer berri? interfazearen aurrebista"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Jar gaitezen egunean"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Jarri egunean jarraitzen diezunen bidalketekin."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Erakutsidazu…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "gehienez ere"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Jarri egunean"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Azken eguneraketarekin gainjartzen da"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Azken eguneraketara arte ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Oharra: litekeena da instantziak gehienez (gutxi gorabehera) 800 bidalketa erakustea hasierako denbora-lerroan, denbora tartea izaten dena izaten dela."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Lehenago…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {bidalketa #} other {# bidalketa}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Eguneraketa kendu?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "{0} eguneraketa kentzen"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "{0} eguneraketa kendu da"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Oharra: gehienez 3 gordeko dira. Gainerakoak automatikoki kenduko dira."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Bidalketak eskuratzen…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Litekeena da donbora behar izatea."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Berrezarri iragazkiak"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Gori-gorian dauden estekak"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "{0}(e)k partekatua"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:329
msgid "All"
msgstr "Guztia"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {egile #} other {# egile}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Sailkatu"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Data"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Dentsitatea"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "Iragazi"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Egileak"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Bat ere ez"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Erakutsi egile guztiak"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Ez duzu zertan guztia irakurri."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "Hori da guztia."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Itzuli gora"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Jarraitzen dituzunek partekatutako estekak, partekatutako, bultzatutako eta gogoko egindako zenbakiagatik sailkatuta."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Sailkatu: dentsitatea"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Bidalketak informazioaren dentsitatearen edo sakoneraren arabera sailkatzen dira. Postu motzenak \"arinagoak\" dira, eta luzeenak, berriz, \"astunagoak\". Argazkidun bidalketak argazkirik gabekoak baino \"astunagoak\" dira."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Taldekatu: egileak"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Bidalketak egilearen arabera taldekatzen dira, egilearen bidalketen kopuruaren arabera sailkatuta."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Hurrengo egilea"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Aurreko egilea"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Korritu gora"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Ez dugu gogokorik. Zoaz gogoko zerbait aurkitzera!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Ezin dira gogokoak kargatu."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Hasiera eta zerrendak"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Denbora-lerro publikoak"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Elkarrizketak"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Profilak"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Inoiz ez"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Iragazki berria"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {Iragazki #} other {# iragazki}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Ezin dira iragazkiak kargatu."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Oraindik ez dago iragazkirik."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Gehitu iragazkia"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Editatu iragazkia"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Ezin da iragazkia editatu"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Ezin da iragazkia sortu"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Izenburua"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Hitz osoa"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Ez dago hitz-gakorik."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Gehitu hitz-gakoa"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {hitz-gako #} other {# hitz-gako}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Iragazi…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Inplementatzeke"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Egoera: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Aldatu iraungitzea"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Iraungitzea"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "Iragazitako bidalketak…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "ilunduta (soilik multimedia)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "txikituko dira"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "ezkutatuko dira"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Iragazkia ezabatu nahi duzu?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Ezin da iragazkia ezabatu."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Iraungi da"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "<0/>(e)an iraungiko da"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Ez da inoiz iraungiko"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {traola #} other {# traola}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Ezin dira jarraitutako traolak kargatu."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Oraindik ez duzu traolarik jarraitzen."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Ez dago ikusteko ezer."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Ezin dira bidalketak kargatu."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (multimedia fitxategiak bakarrik) {instance}(e)n"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} {instance}(e)n"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (multimedia fitxategiak bakarrik)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Oraindik ez du inork traola hau erabili."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Ezin dira etiketa hau duten bidalketak kargatu"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "#{hashtag} jarraitzeari utzi?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "#{hashtag} jarraitzeari utzi diozu"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "#{hashtag} jarraitzen ari zara"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Jarraitzen…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Utzi profilean nabarmentzeari"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Ezin da profilean nabarmentzen uzteari utzi"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Profilean nabarmentzen"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, one {}other {Gehienez # traola}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Gehitu traola"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Kendu traola"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {Gehienezko lasterbidera (#) iritsi zara. Ezin da lasterbidea gehitu.} other {Gehienezko # lasterbideetara iritsi zara. Ezin da lasterbidea gehitu.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Lasterbidea badago lehendik ere"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Traolaren lasterbidea gehitu da"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Gehitu lasterbideetara"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Sartu instantzia berria, adib. \"mastodon.eus\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Instantzia baliogabea"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Joan beste instantzia batera…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Joan nire instantziara (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Ezin dira jakinarazpenak eskuratu."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<1>Jarraipen-eskaera</1> <0>berria</0>"

#: src/pages/home.jsx:251
msgid "See all"
msgstr "Ikusi guztia"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Ebazten…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "Ezin da URLa ebatzi"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Oraindik ezer ez."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Kudeatu kideak"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "<0>@{0}</0> zerrendatik kendu nahi?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Kendu…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {zerrenda #} other {zerrenda #}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Oraindik ez dago zerrendarik."

#: src/pages/login.jsx:118
#: src/pages/login.jsx:128
msgid "Failed to register application"
msgstr "Aplikazioa erregistratzeak huts egin du"

#: src/pages/login.jsx:214
msgid "instance domain"
msgstr "instantziaren domeinua"

#: src/pages/login.jsx:238
msgid "e.g. “mastodon.social”"
msgstr "adib. \"mastodon.eus\""

#: src/pages/login.jsx:249
msgid "Failed to log in. Please try again or try another instance."
msgstr "Ezin da saioa hasi. Saiatu berriro edo saiatu beste instantzia batean."

#: src/pages/login.jsx:261
msgid "Continue with {selectedInstanceText}"
msgstr "Jarraitu {selectedInstanceText}(r)ekin"

#: src/pages/login.jsx:262
msgid "Continue"
msgstr "Jarraitu"

#: src/pages/login.jsx:270
msgid "Don't have an account? Create one!"
msgstr "Oraindik ez duzu konturik? Sortu ezazu!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Aipamen pribatuak"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Pribatua"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Ez zaitu inork aipatu :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Ezin dira aipamenak kargatu."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Jarraitzen ez ditudanenak"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Jarraitzen ez nautenenak"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "Kontu berria dutenenak"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Nik nahi gabe era pribatuan aipatu nautenenak"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Zerbitzariko moderatzaileek muga jarri dietenak"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Jakinarazpenen ezarpenak"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Jakinarazpen berriak"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Iragarpena} other {Iragarpenak}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Jarraipen-eskaerak"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {Jarraipen-eskaera #} other {# jarraipen-eskaera}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Pertsona #en iragazitako jakinarazpenak} other {# pertsonaren iragazitako jakinarazpenak}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Aipamenak soilik"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Gaur"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Zaharrak berri."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Atzo"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Ezin dira jakinarazpenak kargatu"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Jakinarazpenen ezarpenak eguneratu dira"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Ez erakutsi hauen jakinarazpenak:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Iragazi"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ez hartu kontuan"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "<0>{0}</0>(e)an eguneratu zen"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Ikusi <0>@{0}</0>(r)en jakinarazpenak"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "<0>@{0}</0>(r)en jakinarazpenak"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Aurrerantzean ez dira @{0}(r)en jakinarazpenak iragaziko."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "Ezin da jakinarazpen-eskaera onartu"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Baimendu"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "Aurrerantzean ez dira @{0}(r)en jakinarazpenak erakutsiko iragazitako jakinarazpenetan."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "Ezin da jakinarazpen-eskaera zokoratu"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Baztertu"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Baztertuta"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Denbora-lerro lokala ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Denbora-lerro federatua ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Denbora-lerro lokala"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Denbora-lerro federatua"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Oraindik inork ez du ezer argitaratu."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Aldatu federatura"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Aldatu lokalera"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "Ez dago programatutako bidalketarik."

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Programatuta <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Programatuta <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Programatutako bidalketa berriro programatu da"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "Huts egin du bidalketa programatzeak"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Programatu berriro"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "Ezabatu programatutako bidalketa?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "Programatutako bidalketa ezabatu da"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "Huts egin du programatutako bidalketa ezabatzeak"

#: src/pages/search.jsx:50
msgid "Search: {q} (Posts)"
msgstr "Bilatu: {q} (Bidalketak)"

#: src/pages/search.jsx:53
msgid "Search: {q} (Accounts)"
msgstr "Bilatu: {q} (Kontuak)"

#: src/pages/search.jsx:56
msgid "Search: {q} (Hashtags)"
msgstr "Bilatu: {q} (Traolak)"

#: src/pages/search.jsx:59
msgid "Search: {q}"
msgstr "Bilatu: {q}"

#: src/pages/search.jsx:339
#: src/pages/search.jsx:421
msgid "Hashtags"
msgstr "Traolak"

#: src/pages/search.jsx:371
#: src/pages/search.jsx:425
#: src/pages/search.jsx:495
msgid "See more"
msgstr "Ikusi gehiago"

#: src/pages/search.jsx:397
msgid "See more accounts"
msgstr "Ikusi kontu gehiago"

#: src/pages/search.jsx:411
msgid "No accounts found."
msgstr "Ez da konturik aurkitu."

#: src/pages/search.jsx:467
msgid "See more hashtags"
msgstr "Ikusi traola gehiago"

#: src/pages/search.jsx:481
msgid "No hashtags found."
msgstr "Ez da traolarik aurkitu."

#: src/pages/search.jsx:525
msgid "See more posts"
msgstr "Ikusi bidalketa gehiago"

#: src/pages/search.jsx:539
msgid "No posts found."
msgstr "Ez da bidalketarik aurkitu."

#: src/pages/search.jsx:583
msgid "Enter your search term or paste a URL above to get started."
msgstr "Sartu bilatzeko terminoa edo itsatsi URLa gainean."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Ezarpenak"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Itxura"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Argia"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Iluna"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Automatikoa"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Testuaren tamaina"

#. Preview of one character, in smallest size
#. Preview of one character, in largest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Pantaila-hizkuntza"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Eskaini burua itzultzeko"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Argitaratzean"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Ikusgaitasuna, defektuz"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Sinkronizatuta"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Ezin da bidalketaren pribatutasuna eguneratu"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Zure zerbitzariko ezarpenekin sinkronizatu da. <0>Joan zure instantziara ({instance}) ezarpen gehiagorako.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Esperimentuak"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Automatikoki freskatu denbora-lerroko bidalketak"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Bultzaden karrusela"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Bidalketen itzulpena"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Itzuli… "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Sistemak darabilena ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, one {}=0 {Ezkutatu \"Itzuli\" botoia honentzat:} other {Ezkutatu \"Itzuli\" botoia (#) hauentzat:}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "Oharra: ezaugarri honek hirugarrenen itzulpen-zerbitzuak darabiltza, <0>{TRANSLATION_API_NAME}</0>(r)i esker."

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Itzulpen automatikoa bidalketan bertan"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Erakutsi automatikoki bidalketen itzulpena denbora-lerroaren baitan. Bidalketa <0>labur</0>retarako balio du bakarrik, eta ezin dute edukiari buruzko oharrik, multimedia fitxategirik edo bozketarik izan."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "GIF hautatzailea"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Oharra: ezaugarri honek hirugarrenen zerbitzua darabil GIFen bilaketarako, <0>GIPHY</0>k eskainia. Adin guztietarako egokia da, jarraipen parametroak ezabatu egiten dira, jatorriaren informazioa eskarietatik kanpo uzten da, baina bilaketa-kontsultek eta IP helbidearen informazioak bere zerbitzarietara iristen jarraituko dute."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Irudien deskribapen-sortzailea"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Soilik irudi berrientzat bidalketa berriak idaztean."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Oharra: ezaugarri honek hirugarrenen AA zerbitzua darabil, <0>img-alt-api</0>k eskainia. Litekeena da erabat ondo ez egitea. Soilik irudientzat eta soilik ingelesez."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Zerbitzariak taldekatutako jakinarazpenak"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Alpha fasean dagoen ezaugarria. Taldekatzea hobetu lezake, baina oinarrizko logika erabiliz."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "Lasterbideen hodeiko inportazio / esportazio ezarpenak"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Oso esperimentala.<0/>Zure profileko oharretan gordetzen da. Profileko oharrak (pribatuak) beste profil batzuei buruzko oharretarako erabiltzen dira nagusiki, eta norberaren profilean ezkutatuta daude."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Oharra: ezaugarri honek saio hasita duzun zerbitzariaren APIa darabil."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Eskalki modua <0>(<1>Testua</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Testua blokeekin ordezkatzen du, pantaila-argazkiak egitean aproposa pribatutasun arrazoiengatik."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Honi buruz"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<1>@cheeaun</1>ek <0>sortua</0>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Eman babesa"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Egin dohaintza"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr ""

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Pribatutasun politika"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Gunea:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Bertsioa:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Bertsioaren haria kopiatuta"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "Ezin da bertsioaren haria kopiatu"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "Ezin da harpidetza eguneratu. Saiatu berriro."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "Ezin da harpidetza kendu. Saiatu berriro."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Push jakinarazpenak (beta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Push jakinarazpenak blokeatuta daude. Gaitu itzazu nabigatzaileko ezarpenetan."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Baimendu <0>{0}</0>(r)en"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "edonor"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "jarraitzen diodan jendea"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "jarraitzaile"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Jarraitzen die"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Bozketak"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Bidalketen edizioak"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "Ez da push baimenik eman saioa azkenekoz hasi zenuenetik. <0><1>Hasi saioa</1> berriro baimentzeko</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "Oharra: push jakinarazpenak <0>kontu bakarrarentzat</0> dabiltza."

#. js-lingui-explicit-id
#: src/pages/status.jsx:657
#: src/pages/status.jsx:1220
msgid "post.title"
msgstr "Bidalketa"

#: src/pages/status.jsx:911
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Ez duzu saiorik hasi. Ezin duzu interaktuatu (erantzun, bultzatu...)"

#: src/pages/status.jsx:931
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Bidalketa hau beste instantzia batekoa da (<0>{instance}</0>). Interakzioak (erantzunak, bultzadak, etab.) ez dira posible."

#: src/pages/status.jsx:959
msgid "Error: {e}"
msgstr "Errorea: {e}"

#: src/pages/status.jsx:966
msgid "Switch to my instance to enable interactions"
msgstr "Aldatu nire instantziara interakzioak gaitzeko"

#: src/pages/status.jsx:1068
msgid "Unable to load replies."
msgstr "Ezin dira erantzunak kargatu."

#: src/pages/status.jsx:1180
msgid "Back"
msgstr "Atzera"

#: src/pages/status.jsx:1211
msgid "Go to main post"
msgstr "Joan bidalketa nagusira"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1234
msgid "{0} posts above ‒ Go to top"
msgstr "{0} bidalketa goian ‒ Joan gora"

#: src/pages/status.jsx:1281
#: src/pages/status.jsx:1344
msgid "Switch to Side Peek view"
msgstr "Aldatu gainbegirada bistara"

#: src/pages/status.jsx:1345
msgid "Switch to Full view"
msgstr "Aldatu bista osora"

#: src/pages/status.jsx:1363
msgid "Show all sensitive content"
msgstr "Erakutsi eduki hunkigarri guztia"

#: src/pages/status.jsx:1368
msgid "Experimental"
msgstr "Esperimentala"

#: src/pages/status.jsx:1377
msgid "Unable to switch"
msgstr "Ezin da aldatu"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1384
msgid "Switch to post's instance ({0})"
msgstr "Aldatu bidalketaren instantziara ({0})"

#: src/pages/status.jsx:1387
msgid "Switch to post's instance"
msgstr "Aldatu bidalketaren instantziara"

#: src/pages/status.jsx:1445
msgid "Unable to load post"
msgstr "Ezin da bidalketa kargatu"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1581
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {erantzun #} other {<0>{1}</0> erantzun}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1599
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {iruzkin #} other {<0>{0}</0> iruzkin}}"

#: src/pages/status.jsx:1621
msgid "View post with its replies"
msgstr "Ikusi bidalketa eta erantzunak"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Joerak ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Pil-pilean dauden albisteak"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "{0}(r)en eskutik"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Itzuli bogan dauden bidalketetara"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "<0>{0}</0> aipatzen duten bidalketak erakusten"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Bogan dauden bidalketak"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Ez dago bogadn dagoen joerarik."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Mastodon erabiltzeko web-bezero minimalista eta aparta."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Hasi saioa Mastodon-ekin"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Eman izena"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Konektatu zure Mastodon / Fedibertsoko kontua.<0/>Zure egiaztagiriak ez dira zerbitzari honetan gordetzen."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<1>@cheeaun</1>ek <0>sortua</0>. <2>Pribatutasun politika</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Bultzaden karruselaren pantaila-argazkia"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Bultzaden karrusela"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Bereizi bisualki bidalketa originalak eta partekatutakoak (bultzadak)."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Habiratutako iruzkinen hariaren pantaila-argazkia"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Habiratutako iruzkinen haria"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Jarraitu elkarrizketak arazorik gabe. Erantzunak tolesten dira."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Taldekatutako jakinarazpenen pantaila-argazkia"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Taldekatutako jakinarazpenak"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Antzeko jakinarazpenak taldekatzen eta tolesten dira anabasa gutxitzeko."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Zutabe anitzeko interfazearen pantaila-argazkia"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Nahi beste zutabe"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Defektuz, zutabe bakarra ZEN antolaketa nahi dutenentzako. Zutabe gehiago konfiguratu daitezke erabilera aurreraturako."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Traola anitzeko denbora-lerroaren pantaila-argazkia, traola gehiago gehitzeko inprimakiarekin"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Traola anitzeko denbora-lerroa"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Denbora-lerro bakarrean 5 traola ere batu daitezke."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Badirudi nabigatzailea laster-leihoak blokeatzen ari dela."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Zirriborro bat ikonotuta dago. Argitaratu edo zokoratu berri bat sortu baino lehen."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Bidalketa bat irekita dago. Argitaratu edo zokoratu berri bat sortu baino lehen."

