msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: fr\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-06-20 01:13\n"
"Last-Translator: \n"
"Language-Team: French\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: fr\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:137
msgid "Locked"
msgstr "Verrouillé"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:143
msgid "Posts: {0}"
msgstr "Publications : {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:148
msgid "Last posted: {0}"
msgstr "Dernière publication : {0}"

#: src/components/account-block.jsx:163
#: src/components/account-info.jsx:710
msgid "Automated"
msgstr "Automatisé"

#: src/components/account-block.jsx:170
#: src/components/account-info.jsx:715
#: src/components/status.jsx:590
msgid "Group"
msgstr "Groupe"

#: src/components/account-block.jsx:180
msgid "Mutual"
msgstr "Suivi mutuel"

#: src/components/account-block.jsx:184
#: src/components/account-info.jsx:1875
msgid "Requested"
msgstr "Demandé"

#: src/components/account-block.jsx:188
#: src/components/account-info.jsx:1866
msgid "Following"
msgstr "Abonnements"

#: src/components/account-block.jsx:192
#: src/components/account-info.jsx:1180
msgid "Follows you"
msgstr "Vous suit"

#: src/components/account-block.jsx:200
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# abonné⋅e} other {# abonné⋅es}}"

#: src/components/account-block.jsx:209
#: src/components/account-info.jsx:758
msgid "Verified"
msgstr "Vérifié"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:224
#: src/components/account-info.jsx:860
msgid "Joined <0>{0}</0>"
msgstr "Inscrit·e depuis le <0>{0}</0>"

#: src/components/account-info.jsx:63
msgid "Forever"
msgstr "Pour toujours"

#: src/components/account-info.jsx:403
msgid "Unable to load account."
msgstr "Le compte n’a pas pu être chargé."

#: src/components/account-info.jsx:418
msgid "Go to account page"
msgstr "Aller à la page du compte"

#: src/components/account-info.jsx:447
#: src/components/account-info.jsx:780
#: src/components/account-info.jsx:810
msgid "Followers"
msgstr "Abonné⋅e·s"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:451
#: src/components/account-info.jsx:820
#: src/components/account-info.jsx:839
msgid "following.stats"
msgstr ""

#: src/components/account-info.jsx:454
#: src/components/account-info.jsx:856
#: src/pages/account-statuses.jsx:487
#: src/pages/search.jsx:344
#: src/pages/search.jsx:491
msgid "Posts"
msgstr "Publications"

#: src/components/account-info.jsx:462
#: src/components/account-info.jsx:1236
#: src/components/compose.jsx:2786
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1836
#: src/components/status.jsx:1853
#: src/components/status.jsx:1978
#: src/components/status.jsx:2599
#: src/components/status.jsx:2602
#: src/pages/account-statuses.jsx:531
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1305
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Davantage"

#: src/components/account-info.jsx:474
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> a indiqué que son nouveau compte est désormais :"

#: src/components/account-info.jsx:619
#: src/components/account-info.jsx:1464
msgid "Handle copied"
msgstr "Identifiant copié"

#: src/components/account-info.jsx:622
#: src/components/account-info.jsx:1467
msgid "Unable to copy handle"
msgstr "L’identifiant n’a pas pu être copié."

#: src/components/account-info.jsx:628
#: src/components/account-info.jsx:1473
msgid "Copy handle"
msgstr "Copier l’identifiant"

#: src/components/account-info.jsx:634
msgid "Go to original profile page"
msgstr "Visiter la page de profil originale"

#: src/components/account-info.jsx:652
msgid "View profile image"
msgstr "Afficher l’image de profil"

#: src/components/account-info.jsx:670
msgid "View profile header"
msgstr "Afficher la bannière de profil"

#: src/components/account-info.jsx:686
#: src/components/account-info.jsx:1774
#: src/components/account-info.jsx:2300
msgid "Edit profile"
msgstr "Modifier votre profil"

#: src/components/account-info.jsx:705
msgid "In Memoriam"
msgstr "En mémoire"

#: src/components/account-info.jsx:787
#: src/components/account-info.jsx:830
msgid "This user has chosen to not make this information available."
msgstr "Cette personne ne souhaite pas partager cette information."

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:885
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} publications, {1} réponses, {2} partages"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:901
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {Dernière publication aujourd’hui} other {Dernière publication ces {2} derniers jours}}} other {{3, plural, one {{4} publications les plus récentes aujourd’hui} other {{5} publications les plus récentes ces {6} derniers jours}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:917
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {Dernière publication dans les dernières années} other {{1} dernières publications dans les dernières années}}"

#: src/components/account-info.jsx:942
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Messages"

#: src/components/account-info.jsx:946
#: src/components/status.jsx:2383
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1024
#: src/pages/status.jsx:1651
msgid "Replies"
msgstr "Réponses"

#: src/components/account-info.jsx:950
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Partages"

#: src/components/account-info.jsx:956
msgid "Post stats unavailable."
msgstr "Les statistiques de ce message ne sont pas disponibles."

#: src/components/account-info.jsx:987
msgid "View post stats"
msgstr "Afficher les statistiques du message"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1184
msgid "Last post: <0>{0}</0>"
msgstr "Dernière publication : <0>{0}</0>"

#: src/components/account-info.jsx:1198
msgid "Muted"
msgstr "Masqué⋅e"

#: src/components/account-info.jsx:1203
msgid "Blocked"
msgstr "Bloqué⋅e"

#: src/components/account-info.jsx:1212
msgid "Private note"
msgstr "Note personnelle"

#: src/components/account-info.jsx:1269
msgid "Mention <0>@{username}</0>"
msgstr "Mentionner <0>@{username}</0>"

#: src/components/account-info.jsx:1281
msgid "Translate bio"
msgstr "Traduire la bio"

#: src/components/account-info.jsx:1292
msgid "Edit private note"
msgstr "Modifier la note personnelle"

#: src/components/account-info.jsx:1292
msgid "Add private note"
msgstr "Ajouter une note personnelle"

#: src/components/account-info.jsx:1312
msgid "Notifications enabled for @{username}'s posts."
msgstr "Notifications activées pour les publications de @{username}."

#: src/components/account-info.jsx:1313
msgid " Notifications disabled for @{username}'s posts."
msgstr "Notifications désactivées pour les publications de @{username}."

#: src/components/account-info.jsx:1325
msgid "Disable notifications"
msgstr "Désactiver les notifications"

#: src/components/account-info.jsx:1326
msgid "Enable notifications"
msgstr "Activer les notifications"

#: src/components/account-info.jsx:1343
msgid "Boosts from @{username} enabled."
msgstr "Partages de @{username} affichés."

#: src/components/account-info.jsx:1344
msgid "Boosts from @{username} disabled."
msgstr "Partages de @{username} masqués."

#: src/components/account-info.jsx:1355
msgid "Disable boosts"
msgstr "Masquer les partages"

#: src/components/account-info.jsx:1355
msgid "Enable boosts"
msgstr "Afficher les partages"

#: src/components/account-info.jsx:1374
msgid "@{username} is no longer featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1384
msgid "@{username} is now featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1392
msgid "Unable to unfeature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1396
msgid "Unable to feature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1405
msgid "Don't feature on profile"
msgstr ""

#: src/components/account-info.jsx:1406
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Mettre en avant sur votre profil"

#: src/components/account-info.jsx:1415
msgid "Show featured profiles"
msgstr ""

#: src/components/account-info.jsx:1430
#: src/components/account-info.jsx:1440
#: src/components/account-info.jsx:2046
msgid "Add/Remove from Lists"
msgstr "Ajouter ou retirer des listes"

#: src/components/account-info.jsx:1490
#: src/components/status.jsx:1258
msgid "Link copied"
msgstr "Lien copié"

#: src/components/account-info.jsx:1493
#: src/components/status.jsx:1261
msgid "Unable to copy link"
msgstr "Le lien n’a pas pu être copié."

#: src/components/account-info.jsx:1499
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1267
#: src/components/status.jsx:3377
msgid "Copy"
msgstr "Copier"

#: src/components/account-info.jsx:1514
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1283
msgid "Sharing doesn't seem to work."
msgstr "Le partage ne paraît pas possible."

#: src/components/account-info.jsx:1520
#: src/components/status.jsx:1289
msgid "Share…"
msgstr "Partager…"

#: src/components/account-info.jsx:1540
msgid "Unmuted @{username}"
msgstr "@{username} n’est plus masqué⋅e"

#: src/components/account-info.jsx:1552
msgid "Unmute <0>@{username}</0>"
msgstr "Ne plus masquer <0>@{username}></0>"

#: src/components/account-info.jsx:1568
msgid "Mute <0>@{username}</0>…"
msgstr "Masquer <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1600
msgid "Muted @{username} for {0}"
msgstr "@{username} masqué⋅e pendant {0}"

#: src/components/account-info.jsx:1612
msgid "Unable to mute @{username}"
msgstr "Impossible de masquer @{username}"

#: src/components/account-info.jsx:1633
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Retirer <0>@{username}</0> de vos abonné⋅e·s ?"

#: src/components/account-info.jsx:1653
msgid "@{username} removed from followers"
msgstr "@{username} retiré⋅e de vos abonné⋅es"

#: src/components/account-info.jsx:1665
msgid "Remove follower…"
msgstr "Retirer de vos abonné⋅es…"

#: src/components/account-info.jsx:1676
msgid "Block <0>@{username}</0>?"
msgstr "Bloquer <0>@{username}</0>?"

#: src/components/account-info.jsx:1700
msgid "Unblocked @{username}"
msgstr "@{username} débloqué⋅e"

#: src/components/account-info.jsx:1708
msgid "Blocked @{username}"
msgstr "@{username} bloqué⋅e"

#: src/components/account-info.jsx:1716
msgid "Unable to unblock @{username}"
msgstr "Impossible de débloquer @{username}"

#: src/components/account-info.jsx:1718
msgid "Unable to block @{username}"
msgstr "Impossible de bloquer @{username}"

#: src/components/account-info.jsx:1728
msgid "Unblock <0>@{username}</0>"
msgstr "Débloquer <0>@{username}</0>"

#: src/components/account-info.jsx:1737
msgid "Block <0>@{username}</0>…"
msgstr "Bloquer <0>@{username}</0>…"

#: src/components/account-info.jsx:1754
msgid "Report <0>@{username}</0>…"
msgstr "Signaler <0>@{username}</0>…"

#: src/components/account-info.jsx:1810
msgid "Withdraw follow request?"
msgstr "Annuler la demande de suivi ?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1811
msgid "Unfollow @{0}?"
msgstr "Ne plus suivre @{0} ?"

#: src/components/account-info.jsx:1869
msgid "Unfollow…"
msgstr "Ne plus suivre…"

#: src/components/account-info.jsx:1878
msgid "Withdraw…"
msgstr "Annuler…"

#: src/components/account-info.jsx:1885
#: src/components/account-info.jsx:1889
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Suivre"

#: src/components/account-info.jsx:1986
#: src/components/account-info.jsx:2041
#: src/components/account-info.jsx:2175
#: src/components/account-info.jsx:2295
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:891
#: src/components/compose.jsx:2742
#: src/components/compose.jsx:3222
#: src/components/compose.jsx:3431
#: src/components/compose.jsx:3661
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:75
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3101
#: src/components/status.jsx:3341
#: src/components/status.jsx:3850
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1392
msgid "Close"
msgstr "Fermer"

#: src/components/account-info.jsx:1991
msgid "Translated Bio"
msgstr "Bio traduite"

#: src/components/account-info.jsx:2086
msgid "Unable to remove from list."
msgstr "Impossible de retirer de la liste."

#: src/components/account-info.jsx:2087
msgid "Unable to add to list."
msgstr "Impossible d’ajouter à la liste."

#: src/components/account-info.jsx:2106
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Les listes n’ont pas pu être chargées."

#: src/components/account-info.jsx:2110
msgid "No lists."
msgstr "Aucune liste."

#: src/components/account-info.jsx:2121
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Nouvelle liste"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2180
msgid "Private note about <0>@{0}</0>"
msgstr "Note personnelle à propos de <0>@{0}</0>"

#: src/components/account-info.jsx:2210
msgid "Unable to update private note."
msgstr "Impossible de mettre à jour la note personnelle."

#: src/components/account-info.jsx:2233
#: src/components/account-info.jsx:2531
msgid "Cancel"
msgstr "Annuler"

#: src/components/account-info.jsx:2238
msgid "Save & close"
msgstr "Enregistrer et quitter"

#: src/components/account-info.jsx:2355
msgid "Unable to update profile."
msgstr "Impossible de mettre à jour le profil."

#: src/components/account-info.jsx:2362
msgid "Header picture"
msgstr "Image d’en-tête"

#: src/components/account-info.jsx:2414
msgid "Profile picture"
msgstr "Photo du profil"

#: src/components/account-info.jsx:2466
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Nom"

#: src/components/account-info.jsx:2479
msgid "Bio"
msgstr "Bio"

#: src/components/account-info.jsx:2492
msgid "Extra fields"
msgstr "Champs personnalisés"

#: src/components/account-info.jsx:2498
msgid "Label"
msgstr "Titre"

#: src/components/account-info.jsx:2501
msgid "Content"
msgstr "Contenu"

#: src/components/account-info.jsx:2534
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Enregistrer"

#: src/components/account-info.jsx:2588
msgid "username"
msgstr "identifiant"

#: src/components/account-info.jsx:2592
msgid "server domain name"
msgstr "nom de domaine"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2658
msgid "Profiles featured by @{0}"
msgstr ""

#: src/components/account-info.jsx:2684
msgid "No featured profiles."
msgstr ""

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Mode camouflage désactivé"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Mode camouflage activé"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Accueil"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Rédiger"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Publications planifiées"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Ajouter au fil"

#: src/components/compose.jsx:211
msgid "Take photo or video"
msgstr "Prendre une photo ou une vidéo"

#: src/components/compose.jsx:212
msgid "Add media"
msgstr "Ajouter un média"

#: src/components/compose.jsx:213
msgid "Add custom emoji"
msgstr "Insérer un émoji personnalisé"

#: src/components/compose.jsx:214
msgid "Add GIF"
msgstr "Ajouter un GIF"

#: src/components/compose.jsx:215
msgid "Add poll"
msgstr "Insérer un sondage"

#: src/components/compose.jsx:216
msgid "Schedule post"
msgstr "Planifier la publication"

#: src/components/compose.jsx:416
msgid "You have unsaved changes. Discard this post?"
msgstr "Le message n’est pas sauvegardé. Annuler sa rédaction ?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:654
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {Le fichier {1} n'est pas pris en charge.} other {Les fichiers {2} ne sont pas pris en charge.}}"

#: src/components/compose.jsx:664
#: src/components/compose.jsx:682
#: src/components/compose.jsx:1792
#: src/components/compose.jsx:1917
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Vous ne pouvez joindre qu’un seul fichier.} other {Vous pouvez joindre jusqu’à # fichiers.}}"

#: src/components/compose.jsx:872
msgid "Pop out"
msgstr "Ouvrir dans une nouvelle fenêtre"

#: src/components/compose.jsx:879
msgid "Minimize"
msgstr "Minimiser"

#: src/components/compose.jsx:915
msgid "Looks like you closed the parent window."
msgstr "Il semblerait que vous ayez fermé la fenêtre d’origine."

#: src/components/compose.jsx:922
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Il semblerait que vous ayez déjà un message en cours de publication ouvert dans la fenêtre d’origine. Merci de réessayer une fois ce message publié."

#: src/components/compose.jsx:927
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Il semblerait que vous ayez déjà un message en cours de rédaction dans la fenêtre d’origine. Restaurer cette fenêtre annulera la rédaction du message de la fenêtre d’origine sans sauvegarder. Continuer ?"

#: src/components/compose.jsx:970
msgid "Pop in"
msgstr "Restaurer dans la fenêtre d’origine"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:980
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "En réponse au message de @{0} (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:990
msgid "Replying to @{0}’s post"
msgstr "En réponse au message de @{0}"

#: src/components/compose.jsx:1003
msgid "Editing source post"
msgstr "Modification du message d’origine"

#: src/components/compose.jsx:1056
msgid "Poll must have at least 2 options"
msgstr "Au moins deux choix sont nécessaires pour un sondage"

#: src/components/compose.jsx:1060
msgid "Some poll choices are empty"
msgstr "Tous les choix du sondage ne sont pas renseignés"

#: src/components/compose.jsx:1073
msgid "Some media have no descriptions. Continue?"
msgstr "Les médias n’ont pas tous de descriptions renseignées. Continuer ?"

#: src/components/compose.jsx:1125
msgid "Attachment #{i} failed"
msgstr "#{i} n’a pas pu être joint au message"

#: src/components/compose.jsx:1221
#: src/components/status.jsx:2166
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Avertissement de contenu"

#: src/components/compose.jsx:1237
msgid "Content warning or sensitive media"
msgstr "Avertissement de contenu ou média délicat"

#: src/components/compose.jsx:1273
#: src/components/status.jsx:96
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Public"

#: src/components/compose.jsx:1278
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:97
msgid "Local"
msgstr "Local"

#: src/components/compose.jsx:1282
#: src/components/status.jsx:98
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Non-listé"

#: src/components/compose.jsx:1285
#: src/components/status.jsx:99
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Abonné⋅es seulement"

#: src/components/compose.jsx:1288
#: src/components/status.jsx:100
#: src/components/status.jsx:2042
msgid "Private mention"
msgstr "Mention privée"

#: src/components/compose.jsx:1297
msgid "Post your reply"
msgstr "Publier votre réponse"

#: src/components/compose.jsx:1299
msgid "Edit your post"
msgstr "Modifier votre message"

#: src/components/compose.jsx:1300
msgid "What are you doing?"
msgstr "Quoi de neuf ?"

#: src/components/compose.jsx:1379
msgid "Mark media as sensitive"
msgstr "Marquer le média comme délicat"

#: src/components/compose.jsx:1416
msgid "Posting on <0/>"
msgstr "Publier sur <0/>"

#: src/components/compose.jsx:1447
#: src/components/compose.jsx:3280
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Insérer"

#: src/components/compose.jsx:1673
msgid "Schedule"
msgstr "Planifier"

#: src/components/compose.jsx:1675
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1030
#: src/components/status.jsx:1816
#: src/components/status.jsx:1817
#: src/components/status.jsx:2503
msgid "Reply"
msgstr "Répondre"

#: src/components/compose.jsx:1677
msgid "Update"
msgstr "Mettre à jour"

#: src/components/compose.jsx:1678
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Publier"

#: src/components/compose.jsx:1804
msgid "Downloading GIF…"
msgstr "Téléchargement du GIF…"

#: src/components/compose.jsx:1832
msgid "Failed to download GIF"
msgstr "Le GIF n’a pas pu être téléchargé."

#: src/components/compose.jsx:2047
#: src/components/compose.jsx:2124
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Plus…"

#: src/components/compose.jsx:2556
msgid "Uploaded"
msgstr "Chargé"

#: src/components/compose.jsx:2569
msgid "Image description"
msgstr "Description de l’image"

#: src/components/compose.jsx:2570
msgid "Video description"
msgstr "Description de la vidéo"

#: src/components/compose.jsx:2571
msgid "Audio description"
msgstr "Description de l’audio"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2606
#: src/components/compose.jsx:2626
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Ce fichier est trop lourd. Son chargement pourrait échouer. Essayez de réduire son poids de {0} à {1} ou moins."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2618
#: src/components/compose.jsx:2638
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "Ce média est trop grand. Son chargement pourrait échouer. Essayez de réduire sa taille de {0}×{1}px à {2}×{3}px."

#: src/components/compose.jsx:2646
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Ce média a une fréquence trop élevée. Son chargement pourrait échouer."

#: src/components/compose.jsx:2706
#: src/components/compose.jsx:2956
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Supprimer"

#: src/components/compose.jsx:2723
#: src/compose.jsx:84
msgid "Error"
msgstr "Erreur"

#: src/components/compose.jsx:2748
msgid "Edit image description"
msgstr "Modifier la description de l’image"

#: src/components/compose.jsx:2749
msgid "Edit video description"
msgstr "Modifier la description de la vidéo"

#: src/components/compose.jsx:2750
msgid "Edit audio description"
msgstr "Modifier la description de l’audio"

#: src/components/compose.jsx:2795
#: src/components/compose.jsx:2844
msgid "Generating description. Please wait…"
msgstr "Description en cours de génération. Merci de patienter…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2815
msgid "Failed to generate description: {0}"
msgstr "Échec lors de la génération d’une description : {0}"

#: src/components/compose.jsx:2816
msgid "Failed to generate description"
msgstr "Échec lors de la génération d’une description"

#: src/components/compose.jsx:2828
#: src/components/compose.jsx:2834
#: src/components/compose.jsx:2880
msgid "Generate description…"
msgstr "Générer une description…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2867
msgid "Failed to generate description{0}"
msgstr "Échec lors de la génération d’une description{0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2882
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— expérimental</0>"

#: src/components/compose.jsx:2901
msgid "Done"
msgstr "Enregistrer"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2937
msgid "Choice {0}"
msgstr "Choix {0}"

#: src/components/compose.jsx:2984
msgid "Multiple choices"
msgstr "Choix multiples"

#: src/components/compose.jsx:2987
msgid "Duration"
msgstr "Durée"

#: src/components/compose.jsx:3018
msgid "Remove poll"
msgstr "Supprimer le sondage"

#: src/components/compose.jsx:3239
msgid "Search accounts"
msgstr "Rechercher des comptes"

#: src/components/compose.jsx:3293
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Les comptes n’ont pas pu être chargés"

#: src/components/compose.jsx:3437
msgid "Custom emojis"
msgstr "Émojis personnalisés"

#: src/components/compose.jsx:3457
msgid "Search emoji"
msgstr "Rechercher un émoji"

#: src/components/compose.jsx:3488
msgid "Error loading custom emojis"
msgstr "Les émojis personnalisés n’ont pas pu être chargés"

#: src/components/compose.jsx:3499
msgid "Recently used"
msgstr "Récemment utilisé"

#: src/components/compose.jsx:3500
msgid "Others"
msgstr "Autres"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3538
msgid "{0} more…"
msgstr "{0} de plus…"

#: src/components/compose.jsx:3676
msgid "Search GIFs"
msgstr "Rechercher des GIF"

#: src/components/compose.jsx:3691
msgid "Powered by GIPHY"
msgstr "Propulsé par GIPHY"

#: src/components/compose.jsx:3699
msgid "Type to search GIFs"
msgstr "Entrez votre recherche pour trouver des GIFs"

#: src/components/compose.jsx:3797
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Précédent"

#: src/components/compose.jsx:3815
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Suivant"

#: src/components/compose.jsx:3832
msgid "Error loading GIFs"
msgstr "Erreur lors du chargement des GIFs"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Brouillons non envoyés"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "On dirait que vous avez des brouillons non envoyés. Continuons là où vous l'avez laissé."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Supprimer ce brouillon ?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Erreur lors de la suppression du brouillon. Veuillez réessayer."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1433
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Supprimer…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Erreur lors de la récupération du statut de la réponse !"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Supprimer tous les brouillons ?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Erreur lors de la suppression des brouillons ! Veuillez réessayer."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Tout supprimer…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Pas de brouillon trouvé."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Sondage"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:367
msgid "Media"
msgstr "Média"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Ouvrir dans une nouvelle fenêtre"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Accepter"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Rejeter"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Acceptée"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Rejetée"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:334
#: src/pages/search.jsx:367
msgid "Accounts"
msgstr "Comptes"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:561
#: src/pages/status.jsx:1425
msgid "Show more…"
msgstr "Voir plus…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:566
msgid "The end."
msgstr "C'est fini."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Rien à afficher"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Raccourcis clavier"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Aide pour les raccourcis clavier"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Message suivant"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Message précédent"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Passer le carrousel au message suivant"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Maj</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Passer le carrousel au message précédent"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Maj</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Charger de nouveaux messages"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Ouvrir les détails du message"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Entrée</0> ou <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Développer l'avertissement de contenu ou<0/>activer/désactiver le fil étendu/réduit"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Fermer le message ou les boîtes de dialogue"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Échap</0> ou <1>Retour arrière</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Activer une colonne en mode multi-colonnes"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> à <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Activer la colonne suivante en mode multi-colonnes"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Activer la colonne précédente en mode multi-colonnes"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Rédiger un nouveau message"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Rédiger un nouveau message (nouvelle fenêtre)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Maj</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Publier message"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Entrée</1> ou <2>⌘</2> + <3>Entrée</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:73
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:46
#: src/pages/search.jsx:316
msgid "Search"
msgstr "Recherche"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Répondre (nouvelle fenêtre)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Maj</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Ajouter en favori"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> ou <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1038
#: src/components/status.jsx:2530
#: src/components/status.jsx:2553
#: src/components/status.jsx:2554
msgid "Boost"
msgstr "Partager"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Maj</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1101
#: src/components/status.jsx:2578
#: src/components/status.jsx:2579
msgid "Bookmark"
msgstr "Ajouter aux signets"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Activer/Désactiver le mode camouflage"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Maj</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Modifier la liste"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "La liste n’a pas pu être modifiée."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "La liste n’a pas pu être créée."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Afficher les réponses aux membres de la liste"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Afficher les réponses aux personnes que je suis"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Ne pas afficher les réponses"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Masquer les messages de cette liste sur l'Accueil/Abonnements"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Créer"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Supprimer cette liste ?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "La liste n’a pas pu être supprimée."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr ""

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Description du média"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1144
#: src/components/status.jsx:1153
#: src/components/translation-block.jsx:237
msgid "Translate"
msgstr "Traduire"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1172
msgid "Speak"
msgstr "Prononcer"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Ouvrir le média d'origine dans une nouvelle fenêtre"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Ouvrir le média d'origine"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Tentative de description de l'image. Veuillez patienter…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "La description de l'image a échoué"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Décrivez l'image…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Voir le message"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Média délicat"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Filtré : {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3680
#: src/components/status.jsx:3776
#: src/components/status.jsx:3854
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Filtré"

#: src/components/media.jsx:469
msgid "Open file"
msgstr "Ouvrir le fichier"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Publication planifiée"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Message publié. Voir le message."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Réponse planifiée"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Réponse publiée. Consultez-la."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Message mis à jour. Voir le message."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Menu"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Recharger la page maintenant pour la mettre à jour ?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Nouvelle mise à jour disponible…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr ""

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Rattrapage"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Mentions"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Notifications"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Nouveau"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Profil"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Signets"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Favoris"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Hashtags suivis"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:333
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Filtres"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Comptes masqués"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Comptes masqués…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Compte bloqués"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Comptes bloqués…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Comptes…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:195
#: src/pages/status.jsx:924
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Connexion"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Tendances"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Fédéré"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Raccourcis / Colonnes…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Préférences…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Listes"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Toutes les listes"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Notification"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Cette notification provient de votre autre compte."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Voir toutes les notifications"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} a réagi à votre message avec {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} a publié un message."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} a partagé votre réponse.} other {{account} a partagé votre message.}}} other {{account} a partagé {postsCount} de vos messages.}}} other {{postType, select, reply {<0><1>{0}</1> personnes </0> ont partagé votre réponse.} other {<2><3>{1}</3> personnes</2> ont partagé votre message.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, one {}=1 {{account} vous a suivi.} other {<0><1>{0}</1> personnes</0> vous ont suivi.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} a demandé à vous suivre."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} a aimé votre réponse.} other {{account} a aimé votre message.}}} other {{account} a aimé {postsCount} de vos messages.}}} other {{postType, select, reply {<0><1>{0}</1> personnes </0> ont aimé votre réponse.} other {<2><3>{1}</3> personnes</2> ont aimé votre message.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Un sondage que vous avez créé ou auquel vous avez répondu est terminé."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Un sondage que vous avez créé est terminé."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Un sondage auquel vous avez répondu est maintenant terminé."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Un message auquel vous avez réagi a été modifié."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} a boosté et aimé votre réponse.} other {{account} a boosté et aimé votre message.}}} other {{account} a boosté et aimé {postsCount} de vos messages.}}} other {{postType, select, reply {<0><1>{0}</1> personnes </0> ont boosté et aimé votre réponse.} other {<2><3>{1}</3> personnes</2> ont boosté et aimé votre message.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} s'est inscrit·e."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} a signalé {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Connexions perdues avec <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Avertissement de modération"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Votre #Wrapstodon {year} est arrivé !"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Un·e administrateur·rice de <0>{from}</0> a suspendu <1>{targetName}</1>, ce qui signifie que vous ne pourrez plus recevoir ses mises à jour ou interagir avec lui."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "L’administration de <0>{from}</0> a bloqué <1>{targetName}</1>. Le blocage concerne {followersCount} de vos abonné⋅es et {followingCount} de vos abonnements."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Vous avez bloqué <0>{targetName}</0>. Le blocage concerne {followersCount} de vos abonné⋅es et {followingCount} de vos abonnements."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Votre compte a reçu un avertissement de modération."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Votre compte a été désactivé."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Certains de vos messages ont été marqués comme délicats."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Certaines de vos messages ont été supprimés."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "Vos messages seront dorénavant marqués comme délicats."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Votre compte est restreint."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Votre compte a été suspendu."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Notification de type inconnu : {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1115
#: src/components/status.jsx:1125
msgid "Boosted/Liked by…"
msgstr "Partagée par / Aimée par …"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Aimée par…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Partagé par…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Suivi par…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "En savoir plus <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Afficher #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:328
msgid "Read more →"
msgstr "Lire la suite →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "A voté"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# vote} other {# votes}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Masquer les résultats"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Voter"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1294
#: src/pages/status.jsx:1317
msgid "Refresh"
msgstr "Actualiser"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Montrer les résultats"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> votant} other {<1>{1}</1> votants}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Est clôturé <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Est clos"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Se termine <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Sera clos"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}m"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}h"

#: src/components/report-modal.jsx:29
msgid "Spam"
msgstr "Spam"

#: src/components/report-modal.jsx:30
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Liens malveillants, faux engagement ou réponses répétitives"

#: src/components/report-modal.jsx:33
msgid "Illegal"
msgstr "Illégal"

#: src/components/report-modal.jsx:34
msgid "Violates the law of your or the server's country"
msgstr "Viole la loi de votre pays ou celui du serveur"

#: src/components/report-modal.jsx:37
msgid "Server rule violation"
msgstr "Violation de règle du serveur"

#: src/components/report-modal.jsx:38
msgid "Breaks specific server rules"
msgstr "Viole des règles spécifiques du serveur"

#: src/components/report-modal.jsx:39
msgid "Violation"
msgstr "Infraction"

#: src/components/report-modal.jsx:42
msgid "Other"
msgstr "Autre"

#: src/components/report-modal.jsx:43
msgid "Issue doesn't fit other categories"
msgstr "Le problème ne correspond pas aux autres catégories"

#: src/components/report-modal.jsx:68
msgid "Report Post"
msgstr "Signaler le message"

#: src/components/report-modal.jsx:68
msgid "Report @{username}"
msgstr "Signaler @{username}"

#: src/components/report-modal.jsx:104
msgid "Pending review"
msgstr "En attente de révision"

#: src/components/report-modal.jsx:146
msgid "Post reported"
msgstr "Message signalé"

#: src/components/report-modal.jsx:146
msgid "Profile reported"
msgstr "Profil signalé"

#: src/components/report-modal.jsx:154
msgid "Unable to report post"
msgstr "Le message n’a pas pu être signalé"

#: src/components/report-modal.jsx:155
msgid "Unable to report profile"
msgstr "Le profil n’a pas pu être signalé."

#: src/components/report-modal.jsx:163
msgid "What's the issue with this post?"
msgstr "Quel est le problème avec ce message ?"

#: src/components/report-modal.jsx:164
msgid "What's the issue with this profile?"
msgstr "Quel est le problème avec ce profil ?"

#: src/components/report-modal.jsx:233
msgid "Additional info"
msgstr "Informations complémentaires"

#: src/components/report-modal.jsx:256
msgid "Forward to <0>{domain}</0>"
msgstr "Transférer vers <0>{domain}</0>"

#: src/components/report-modal.jsx:266
msgid "Send Report"
msgstr "Envoyer le rapport"

#: src/components/report-modal.jsx:275
msgid "Muted {username}"
msgstr "Compte {username} masqué"

#: src/components/report-modal.jsx:278
msgid "Unable to mute {username}"
msgstr "{username} n’a pas pu être masqué."

#: src/components/report-modal.jsx:283
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Signaler <0>+ Masquer le profil</0>"

#: src/components/report-modal.jsx:294
msgid "Blocked {username}"
msgstr "Compte {username} bloqué"

#: src/components/report-modal.jsx:297
msgid "Unable to block {username}"
msgstr "{username} n’a pas pu être bloqué."

#: src/components/report-modal.jsx:302
msgid "Send Report <0>+ Block profile</0>"
msgstr "Signaler <0>+ Bloquer le profil</0>"

#: src/components/search-form.jsx:203
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ comptes, hashtags et messages</0>"

#: src/components/search-form.jsx:216
msgid "Posts with <0>{query}</0>"
msgstr "Messages avec <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:228
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Messages avec le mot-clé <0>#{0}</0>"

#: src/components/search-form.jsx:242
msgid "Look up <0>{query}</0>"
msgstr "Rechercher <0>{query}</0>"

#: src/components/search-form.jsx:253
msgid "Accounts with <0>{query}</0>"
msgstr "Comptes avec <0>{query}</0>"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Accueil / Abonnements"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Public (local / fédéré)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Compte"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Hashtag"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "ID de la liste"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Local uniquement"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:199
msgid "Instance"
msgstr "Instance"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Facultatif, par exemple “mastodon.social”"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Terme de recherche"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Facultatif, sauf pour le mode multicolonnes"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "ex : PixelArt (max 5, séparés par des espaces)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Média uniquement"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Raccourcis"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "bêta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Spécifiez une liste de raccourcis qui apparaîtront comme :"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Bouton flottant"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Onglet/Barre de menu"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Multi-colonnes"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Non disponible dans le mode d'affichage actuel"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Déplacer vers le haut"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Déplacer vers le bas"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1395
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Modifier"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Ajouter plus d'un raccourci/colonne pour que ceci puisse fonctionner."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Aucune colonne pour l'instant. Appuyez sur le bouton Ajouter."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Aucun raccourci pour le moment. Appuyez sur le bouton Ajouter un raccourci."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Vous ne savez pas quoi ajouter ?<0/>Essayez d'abord d'ajouter <1>Accueil / Abonnements et Notifications</1> ."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Maximum de colonnes {SHORTCUTS_LIMIT}"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Maximum de raccourcis {SHORTCUTS_LIMIT}"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Importer/Exporter"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Ajouter une colonne…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Ajouter un raccourci…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "La liste spécifique est facultative. La liste est obligatoire pour le mode multi-colonnes, sinon la colonne ne sera pas affichée."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Pour le mode multi-colonnes, le terme de recherche est obligatoire, sinon la colonne ne sera pas affichée."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Plusieurs hashtags sont pris en charge. Séparés par des espaces."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Modifier le raccourci"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Ajouter un raccourci"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Fil d’actualité"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Liste"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Importer/Exporter des <0>raccourcis</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Importer"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Coller les raccourcis ici"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Chargement des raccourcis depuis votre instance…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Les raccourcis n’ont pas pu être chargés."

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Charger les raccourcis depuis votre instance"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Existe dans les raccourcis actuels"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "La liste pourrait ne pas fonctionner si elle provient d'un autre compte."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Format de paramètres non valide"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Remplacer les raccourcis actuels ?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Seuls les raccourcis qui n'existent pas dans les raccourcis actuels seront ajoutés."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Aucun nouveau raccourci à importer"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Raccourcis importés. Dépassement du maximum {SHORTCUTS_LIMIT}, donc les autres ne sont pas importés."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Raccourcis importés"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Importer et ajouter…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Remplacer les raccourcis actuels ?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Importer les raccourcis ?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "ou remplacer…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Importer…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Exporter"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Raccourcis copiés"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Les raccourcis n’ont pas pu être copiés."

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Les paramètres des raccourcis ont été copiés"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Les paramètres de raccourcis n’ont pas pu être copiés."

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Partager"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Enregistrement des raccourcis sur votre instance…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Raccourcis enregistrés"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Les raccourcis n’ont pas pu être sauvegardés."

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Synchroniser avec votre instance"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0,plural, one{# caractère} other{# caractères}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Raccourcis JSON bruts"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Importer/exporter les paramètres de/vers l'instance du serveur (très expérimental)"

#: src/components/status.jsx:614
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>a partagé</1>"

#: src/components/status.jsx:713
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Désolé, votre instance ne peut pas interagir avec ce message d'une autre instance."

#. placeholder {0}: username || acct
#: src/components/status.jsx:867
msgid "Unliked @{0}'s post"
msgstr "Message de @{0} retiré des favoris"

#. placeholder {0}: username || acct
#: src/components/status.jsx:868
msgid "Liked @{0}'s post"
msgstr "A aimé la publication de {0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:907
msgid "Unbookmarked @{0}'s post"
msgstr "Publication de @{0} retirée des signets"

#. placeholder {0}: username || acct
#: src/components/status.jsx:908
msgid "Bookmarked @{0}'s post"
msgstr "Publication de @{0} ajoutée aux signets"

#: src/components/status.jsx:1007
msgid "Some media have no descriptions."
msgstr "Certains médias n'ont pas de descriptions."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1014
msgid "Old post (<0>{0}</0>)"
msgstr "Ancien message (<0>{0}</0>)"

#: src/components/status.jsx:1038
#: src/components/status.jsx:1078
#: src/components/status.jsx:2530
#: src/components/status.jsx:2553
msgid "Unboost"
msgstr "Annuler le partage"

#: src/components/status.jsx:1054
#: src/components/status.jsx:2545
msgid "Quote"
msgstr "Citer"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1066
#: src/components/status.jsx:1532
msgid "Unboosted @{0}'s post"
msgstr "Partage du message de @{0} annulé"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1067
#: src/components/status.jsx:1533
msgid "Boosted @{0}'s post"
msgstr "Publication de @{0} partagée"

#: src/components/status.jsx:1079
msgid "Boost…"
msgstr "Partager…"

#: src/components/status.jsx:1091
#: src/components/status.jsx:1826
#: src/components/status.jsx:2566
msgid "Unlike"
msgstr "Retirer des favoris"

#: src/components/status.jsx:1092
#: src/components/status.jsx:1826
#: src/components/status.jsx:1827
#: src/components/status.jsx:2566
#: src/components/status.jsx:2567
msgid "Like"
msgstr "Ajouter en favori"

#: src/components/status.jsx:1101
#: src/components/status.jsx:2578
msgid "Unbookmark"
msgstr "Retirer des signets"

#: src/components/status.jsx:1184
msgid "Post text copied"
msgstr "Texte de la publication copié"

#: src/components/status.jsx:1187
msgid "Unable to copy post text"
msgstr "Impossible de copier le texte de la publication"

#: src/components/status.jsx:1193
msgid "Copy post text"
msgstr "Copier le texte de la publication"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1211
msgid "View post by <0>@{0}</0>"
msgstr "Voir le message de <0>@{0}</0>"

#: src/components/status.jsx:1232
msgid "Show Edit History"
msgstr "Afficher l’historique des modifications"

#: src/components/status.jsx:1235
msgid "Edited: {editedDateText}"
msgstr "Modifié : {editedDateText}"

#: src/components/status.jsx:1302
#: src/components/status.jsx:3346
msgid "Embed post"
msgstr "Intégrer le message"

#: src/components/status.jsx:1316
msgid "Conversation unmuted"
msgstr "La discussion n'est plus masquée"

#: src/components/status.jsx:1316
msgid "Conversation muted"
msgstr "Conversation mise en silence"

#: src/components/status.jsx:1322
msgid "Unable to unmute conversation"
msgstr "La conversation n’a pas pu être rétablie."

#: src/components/status.jsx:1323
msgid "Unable to mute conversation"
msgstr "La conversation n’a pas pu être mise en sourdine."

#: src/components/status.jsx:1332
msgid "Unmute conversation"
msgstr "Ne plus masquer la discussion"

#: src/components/status.jsx:1339
msgid "Mute conversation"
msgstr "Mettre la conversation en sourdine"

#: src/components/status.jsx:1355
msgid "Post unpinned from profile"
msgstr "Message détaché du profil"

#: src/components/status.jsx:1356
msgid "Post pinned to profile"
msgstr "Message épinglé au profil"

#: src/components/status.jsx:1361
msgid "Unable to unpin post"
msgstr "Le message n’a pas pu être détaché."

#: src/components/status.jsx:1361
msgid "Unable to pin post"
msgstr "Le message n’a pas pu être épinglé."

#: src/components/status.jsx:1370
msgid "Unpin from profile"
msgstr "Détacher de votre profil"

#: src/components/status.jsx:1377
msgid "Pin to profile"
msgstr "Épingler à votre profil"

#: src/components/status.jsx:1406
msgid "Delete this post?"
msgstr "Supprimer ce message ?"

#: src/components/status.jsx:1422
msgid "Post deleted"
msgstr "Message supprimé"

#: src/components/status.jsx:1425
msgid "Unable to delete post"
msgstr "Le message n’a pas pu être supprimé."

#: src/components/status.jsx:1453
msgid "Report post…"
msgstr "Signaler le message …"

#: src/components/status.jsx:1827
#: src/components/status.jsx:1863
#: src/components/status.jsx:2567
msgid "Liked"
msgstr "Favori"

#: src/components/status.jsx:1860
#: src/components/status.jsx:2554
msgid "Boosted"
msgstr "Partagé"

#: src/components/status.jsx:1870
#: src/components/status.jsx:2579
msgid "Bookmarked"
msgstr "Signet"

#: src/components/status.jsx:1874
msgid "Pinned"
msgstr "Épinglé"

#: src/components/status.jsx:1920
#: src/components/status.jsx:2391
msgid "Deleted"
msgstr "Supprimée"

#: src/components/status.jsx:1961
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# réponse} other {# réponses}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2051
msgid "Thread{0}"
msgstr "Fil{0}"

#: src/components/status.jsx:2129
#: src/components/status.jsx:2191
#: src/components/status.jsx:2287
msgid "Show less"
msgstr "Replier"

#: src/components/status.jsx:2129
#: src/components/status.jsx:2191
msgid "Show content"
msgstr "Afficher le contenu"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2283
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Filtré : {0}"

#: src/components/status.jsx:2287
msgid "Show media"
msgstr "Afficher le média"

#: src/components/status.jsx:2427
msgid "Edited"
msgstr "Modifié"

#: src/components/status.jsx:2504
msgid "Comments"
msgstr "Commentaires"

#. More from [Author]
#: src/components/status.jsx:2804
msgid "More from <0/>"
msgstr "Davantage de <0/>"

#: src/components/status.jsx:3106
msgid "Edit History"
msgstr "Historique des modifications"

#: src/components/status.jsx:3110
msgid "Failed to load history"
msgstr "Échec du chargement de l'historique"

#: src/components/status.jsx:3115
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Chargement en cours…"

#: src/components/status.jsx:3351
msgid "HTML Code"
msgstr "Code HTML"

#: src/components/status.jsx:3368
msgid "HTML code copied"
msgstr "Code HTML copié"

#: src/components/status.jsx:3371
msgid "Unable to copy HTML code"
msgstr "Le code HTML n’a pas pu être copié."

#: src/components/status.jsx:3383
msgid "Media attachments:"
msgstr "Médias attachés :"

#: src/components/status.jsx:3405
msgid "Account Emojis:"
msgstr "Émojis du compte :"

#: src/components/status.jsx:3436
#: src/components/status.jsx:3481
msgid "static URL"
msgstr "URL statique"

#: src/components/status.jsx:3450
msgid "Emojis:"
msgstr "Émojis :"

#: src/components/status.jsx:3495
msgid "Notes:"
msgstr "Remarques :"

#: src/components/status.jsx:3499
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Ceci est statique, non stylisé et sans script. Vous devrez peut-être appliquer vos propres styles et les modifier au besoin."

#: src/components/status.jsx:3505
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Les sondages ne sont pas interactifs, ils deviennent une liste avec le décompte des votes."

#: src/components/status.jsx:3510
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Les pièces jointes de média peuvent être des images, des vidéos, des audios ou tout autre type de fichier."

#: src/components/status.jsx:3516
msgid "Post could be edited or deleted later."
msgstr "Le message pourrait être modifié ou supprimé plus tard."

#: src/components/status.jsx:3522
msgid "Preview"
msgstr "Aperçu"

#: src/components/status.jsx:3531
msgid "Note: This preview is lightly styled."
msgstr "Remarque : Cet aperçu est légèrement stylisé."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3784
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> a partagé"

#: src/components/status.jsx:3897
msgid "Post removed by author."
msgstr ""

#: src/components/status.jsx:3906
msgid "You’re not authorized to view this post."
msgstr ""

#: src/components/status.jsx:3915
msgid "Post pending author approval."
msgstr ""

#: src/components/status.jsx:3924
msgid "Quoting not allowed by the author."
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Nouveaux messages"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1077
#: src/pages/status.jsx:1454
msgid "Try again"
msgstr "Essayez à nouveau"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# partage} other {# partages}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Messages épinglés"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Fils"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Filtré</0> : <1>{0}</1>"

#: src/components/translation-block.jsx:194
msgid "Auto-translated from {sourceLangText}"
msgstr "Traduit automatiquement depuis {sourceLangText}"

#: src/components/translation-block.jsx:232
msgid "Translating…"
msgstr "Traduction en cours…"

#: src/components/translation-block.jsx:235
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Traduire à partir de {sourceLangText} (auto-détect)"

#: src/components/translation-block.jsx:236
msgid "Translate from {sourceLangText}"
msgstr "Traduction depuis {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:264
msgid "Auto ({0})"
msgstr "Auto ({0})"

#: src/components/translation-block.jsx:277
msgid "Failed to translate"
msgstr "La traduction a échoué"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Modification du message d’origine"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "En réponse à @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Cette page peut être fermée."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Fermer la fenêtre"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Connexion requise."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:276
msgid "Go home"
msgstr "Retour à l’accueil"

#: src/pages/account-statuses.jsx:238
msgid "Account posts"
msgstr "Messages du compte"

#: src/pages/account-statuses.jsx:245
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Réponses)"

#: src/pages/account-statuses.jsx:247
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (− Partages)"

#: src/pages/account-statuses.jsx:249
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:251
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Média)"

#: src/pages/account-statuses.jsx:257
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:323
msgid "Clear filters"
msgstr "Effacer les filtres"

#: src/pages/account-statuses.jsx:326
msgid "Clear"
msgstr "Effacer"

#: src/pages/account-statuses.jsx:340
msgid "Showing post with replies"
msgstr "Voir le message avec les réponses"

#: src/pages/account-statuses.jsx:345
msgid "+ Replies"
msgstr "+ Réponses"

#: src/pages/account-statuses.jsx:351
msgid "Showing posts without boosts"
msgstr "Affichage des messages sans les partages"

#: src/pages/account-statuses.jsx:356
msgid "- Boosts"
msgstr "− Partages"

#: src/pages/account-statuses.jsx:362
msgid "Showing posts with media"
msgstr "Afficher les messages avec médias"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:379
msgid "Showing posts tagged with #{0}"
msgstr "Affichage des messages marqués avec #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:418
msgid "Showing posts in {0}"
msgstr "Affichage des publications en {0}"

#: src/pages/account-statuses.jsx:508
msgid "Nothing to see here yet."
msgstr "Rien à voir ici pour le moment."

#: src/pages/account-statuses.jsx:509
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Les messages n’ont pas pu être chargés."

#: src/pages/account-statuses.jsx:550
#: src/pages/account-statuses.jsx:580
msgid "Unable to fetch account info"
msgstr "Les informations sur le compte n’ont pas pu être chargés."

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:557
msgid "Switch to account's instance {0}"
msgstr "Basculer vers l'instance du compte {0}"

#: src/pages/account-statuses.jsx:587
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Passer à mon instance (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:660
msgid "Month"
msgstr "Mois"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Actuel"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Par défaut"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Basculer vers ce compte"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Basculer dans un nouvel onglet/fenêtre"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Voir le profil…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Définir par défaut"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Se déconnecter de <0>@{0}</0> ?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Se déconnecter…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr ""

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Ajouter un compte existant"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Note : Le compte <0>Par défaut</0> sera toujours utilisé à l'ouverture. Si un autre compte est sélectionné, il persistera pour la session."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Aucuns signets pour le moment. Ajouter quelque chose comme signet !"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Les signets n’ont pas pu être chargés."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "dernière heure"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "les 2 dernières heures"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "3 dernières heures"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "4 dernières heures"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "5 dernières heures"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "6 dernières heures"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "7 dernières heures"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "8 dernières heures"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "9 dernières heures"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "10 dernières heures"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "11 dernières heures"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "12 dernières heures"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "12 dernières heures et plus"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Mots-clés suivis"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Groupes"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "{selectedFilterCategory, select, all {Tous types de messages} original {Messages originaux} replies {Messages de réponse} boosts {Partages} followedTags {Mots-clés suivis} groups {Groupes} filtered {Messages filtrés}}, {sortBy, select, createdAt {{sortOrder, select, asc {les plus vieux} desc {les plus récents}}} reblogsCount {{sortOrder, select, asc {les moins partagés} desc {les plus partagés}}} favouritesCount {{sortOrder, select, asc {les moins favoris} desc {les plus favoris}}} repliesCount {{sortOrder, select, asc {le moins de réponses} desc {le plus de réponses}}} density {{sortOrder, select, asc {les moins denses} desc {les plus denses}}}} en premier{groupBy, select, account {, groupé par personne} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Rattrapage <0>bêta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Aide"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "En savoir plus"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Le rattrapage est un fil d’actualité supplémentaire qui propose une vue d’ensemble pour rester à la page avec vos abonnements. Son interface est inspirée par les e-mails et vous permet de filtrer et trier les messages facilement."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Aperçu de l’interface de rattrapage"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "C’est parti"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Restez à la page avec vos abonnements."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Afficher tous les messages qui datent de…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "autant que possible"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Rattrapage"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "En commun avec votre rattrapage précédent"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Depuis le dernier rattrapage ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "À noter : il se peut que votre instance ne montre que 800 messages dans le fil d’actualité, plus ou moins, quelle que soit la tranche horaire choisie."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Précédemment…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# message} other {# messages}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Enlever ce rattrapage ?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "Rattrapage {0} en cours de suppression"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "Rattrapage {0} supprimé"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Seulement trois rattrapages sont sauvegardés. Les autres seront automatiquement supprimés."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Chargement des messages…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Merci de patienter."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Réinitialiser les filtres"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Liens populaires"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Partagé par {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:329
msgid "All"
msgstr "Tous"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# personne} other {# personnes}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Classer"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Date"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Densité"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr ""

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Par personne"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Aucun"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Afficher tout le monde"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Pas la peine de tout lire."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "C’est tout !"

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Remonter"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Ce sont les liens partagés par les personnes que vous suivez, classés selon leur nombre d’apparences, de partages et de favoris."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Classer par densité"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Classe les messages selon leur densité d’information. Les messages plus courts ou sans images sont plus “légers”, les messages plus longs ou avec des images sont plus “lourds”."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Groupé par personne"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Les messages sont regroupées par auteur·ice·s, triés par nombre de messages par auteur·ice."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Personne suivante"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Personne précédente"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Remonter"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Aucun favori pour le moment. Allez mettre quelque chose en favori !"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Les favoris n’ont pas pu être chargés."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Principal et listes"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Fils publics"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Discussions"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Profils"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Jamais"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Nouveau Filtre"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# filtre} other {# filtres}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Les filtres n’ont pas pu être chargés."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Aucun filtre pour l'instant."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Ajouter un filtre"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Modifier le filtre"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Le filtre n’a pas pu être modifié."

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Le filtre n’a pas pu être créé"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Titre"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Mot entier"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Aucun mot clé. Ajoutez-en un."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Ajouter un mot clé"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# mot-clé} other {# mots-clés}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Filtrer depuis…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Pas encore implémentée"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Statut : <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Modifier l'expiration"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Expiration"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "Les messages filtrés seront …"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "flouté (médias uniquement)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "minimisé"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "masqué"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Supprimer ce filtre ?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Le filtre n’a pas pu être supprimé."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Expiré"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Expire <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "N’expire jamais"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# hashtag} other {# hashtags}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Les mot-clés suivis n’ont pas pu être chargés."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Aucun hashtag n'a encore été suivi."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Rien à voir ici."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Les messages n’ont pas pu être chargés."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (Média uniquement) sur {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} sur {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (Média uniquement)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Personne n'a encore publié de message avec ce mot croisillon."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Les messages avec ce mot-clé n’ont pas pu être chargés."

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Ne plus suivre #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "Arrêt du suivi de #{hashtag}"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "Abonné·e à #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Suivre…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Retiré du profil"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Le mot-clé n’a pas pu être retiré du profil."

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Mis en avant sur votre profil"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, other {Max # hashtags}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Ajoute un hashtag"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Retirer le hashtag"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {La limite de # raccourci a été atteinte. Le raccourci n’a pas pu être ajouté.} other {La limite de # raccourcis a été atteinte. Le raccourci n’a pas pu être ajouté.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Ce raccourci existe déjà"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Raccourci du hashtag ajouté"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Ajouter aux raccourcis"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Indiquer une nouvelle instance, par exemple “mastodon.social”"

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Instance incorrecte"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Afficher une autre instance…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Afficher mon instance (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Les notification n’ont pas pu être chargées."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Nouvelle</0> <1>demande d'abonnement</1>"

#: src/pages/home.jsx:251
msgid "See all"
msgstr "Tout voir"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Résolution en cours …"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "L’URL n’a pas été trouvée."

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Rien pour l’instant."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Gestion des membres"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Retirer <0>@{0}</0> de la liste ?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Retirer…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# liste} other {# listes}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Aucune liste pour le moment."

#: src/pages/login.jsx:118
#: src/pages/login.jsx:128
msgid "Failed to register application"
msgstr "Échec de l'enregistrement de l'application"

#: src/pages/login.jsx:214
msgid "instance domain"
msgstr "domaine de l’instance"

#: src/pages/login.jsx:238
msgid "e.g. “mastodon.social”"
msgstr "Par exemple “mastodon.social”"

#: src/pages/login.jsx:249
msgid "Failed to log in. Please try again or try another instance."
msgstr "Connexion échouée. Essayez à nouveau, ou avec une autre instance."

#: src/pages/login.jsx:261
msgid "Continue with {selectedInstanceText}"
msgstr "Continuer sur {selectedInstanceText}"

#: src/pages/login.jsx:262
msgid "Continue"
msgstr "Continuer"

#: src/pages/login.jsx:270
msgid "Don't have an account? Create one!"
msgstr "Pas de compte ? Créez-en un !"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Mentions privées"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Privées"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Personne ne vous a mentionné :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Les mentions n’ont pas pu être chargées."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Que vous ne suivez pas"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Qui ne sont pas abonné·e·s à vous"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "Ayant un nouveau compte"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Qui vous mentionnent en privé de façon inattendue"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Qui sont limité·e·s par les modérateur·rice·s du serveur"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Préférences des notifications"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Nouvelles notifications"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Annonce} other {Annonces}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Demandes d’abonnement"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# demande d'abonnement} other {# demandes d'abonnement}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Notifications filtrées provenant de # personne} other {Notifications filtrées provenant de # personnes}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Mentions seulement"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Aujourd’hui"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Vous avez tout rattrapé."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Hier"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Les notifications n’ont pas pu être chargées."

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Préférences de notification mises à jour"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Masquer les notifications provenant de gens :"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Filtrer"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ignorer"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Mis à jour <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Voir les notifications de <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Notifications de <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Les notifications provenant de @{0} seront dorénavant masquées."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "La demande de notification n’a pas pu être acceptée"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Autoriser"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "Les notifications de @{0} n'apparaîtront plus dans les notifications filtrées, dorénavant."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "La demande de notification n’a pas pu être rejetée"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Rejeter"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Rejetée"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Fil local ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Fil fédéré ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Fil local"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Fil fédéré"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Personne n’a encore rien publié."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Basculer vers le fil fédéré"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Passer au fil local"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "Aucune publication planifiée."

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Planifié <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Planifié <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Publication planifiée reprogrammée"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "Impossible de reprogrammer la publication"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Replanifier"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "Supprimer la publication planifiée ?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "Publication planifiée supprimée"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "Impossible de supprimer la publication planifiée"

#: src/pages/search.jsx:50
msgid "Search: {q} (Posts)"
msgstr "Recherche : {q} (Messages)"

#: src/pages/search.jsx:53
msgid "Search: {q} (Accounts)"
msgstr "Recherche : {q} (Comptes)"

#: src/pages/search.jsx:56
msgid "Search: {q} (Hashtags)"
msgstr "Recherche : {q} (Hashtags)"

#: src/pages/search.jsx:59
msgid "Search: {q}"
msgstr "Rechercher : {q}"

#: src/pages/search.jsx:339
#: src/pages/search.jsx:421
msgid "Hashtags"
msgstr "Hashtags"

#: src/pages/search.jsx:371
#: src/pages/search.jsx:425
#: src/pages/search.jsx:495
msgid "See more"
msgstr "Voir plus"

#: src/pages/search.jsx:397
msgid "See more accounts"
msgstr "Afficher plus de comptes"

#: src/pages/search.jsx:411
msgid "No accounts found."
msgstr "Aucun compte trouvé."

#: src/pages/search.jsx:467
msgid "See more hashtags"
msgstr "Voir plus de hashtags"

#: src/pages/search.jsx:481
msgid "No hashtags found."
msgstr "Aucun hashtag trouvé."

#: src/pages/search.jsx:525
msgid "See more posts"
msgstr "Voir plus de messages"

#: src/pages/search.jsx:539
msgid "No posts found."
msgstr "Aucun message trouvé."

#: src/pages/search.jsx:583
msgid "Enter your search term or paste a URL above to get started."
msgstr "Entrez le terme recherché ou collez une URL ci-dessus pour commencer."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Paramètres"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Affichage"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Clair"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Sombre"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Auto"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Taille du texte"

#. Preview of one character, in smallest size
#. Preview of one character, in largest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Langue d'affichage"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Traductions bénévoles"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Publication"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Visibilité par défaut"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Synchronisé"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Échec de la mise à jour de la confidentialité du mode de publication"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Synchronisé avec les paramètres de votre serveur d'instance. <0>Allez à votre instance ({instance}) pour plus de paramètres.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Expérimentations"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Charger automatiquement les messages du fil d’actualité"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Carrousel des partages"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Traduction des messages"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Traduire en "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Langue système ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, one {}=0 {Masquer le bouton \"Traduire\" pour:} other {Masquer le bouton \"Traduire\" pour (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr ""

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Traduction automatique sur la même ligne"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Affiche automatiquement la traduction des messages sur le fil d’actualité. Ne fonctionne qu’avec les messages <0>courts</0> sans médias, ni sondages, ni avertissement de contenu."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "Sélecteur de GIFs lors de la rédaction"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Note : cette fonctionnalité utilise un moteur de recherche de GIF externe proposé par <0>GIPHY</0>. Seuls les GIF adaptés à toutes tranches d'ages seront affichés, les paramètres de traçage seront supprimés, le référent ne sera pas envoyé avec les requètes, mais leurs serveurs verront tout de même votre adresse IP et vos recherches."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Générateur de description d'images"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Uniquement pour les nouvelles images lors de la rédaction de nouveaux messages."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Remarque : Cette fonction utilise un service IA externe, propulsé par <0>img-alt-api</0>. Peut ne pas fonctionner correctement. Seulement pour les images et en anglais."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Notifications groupées côté serveur"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Fonctionnalité en phase alpha. Fenêtre de regroupement potentiellement améliorée, mais logique de regroupement de base."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "Import/export \"Cloud\" pour les paramètres des raccourcis"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Très expérimental.<0/>Enregistré dans les notes personnelles de votre propre profil. Ces notes, privées, sont généralement utilisées sur les profils des autres, et masquées sur le votre."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Note : Cette fonction utilise l'API du serveur d'instance auquel vous êtes actuellement connecté."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Mode camouflage <0>(<1>Texte</1> → <2>█████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Masque le texte avec des rectangles, pratique pour prendre des captures d’écran de manière respectueuse de la vie privée."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "À propos"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Fait</0> par <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Parrain"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Faire un don"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr ""

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Politique de confidentialité"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Site:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Version:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Texte de version copié"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "Le numéro de version n’a pas pu être copié."

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "La mise à jour de l'abonnement a échoué. Veuillez réessayer."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "La suppression de l'abonnement a échoué. Veuillez réessayer."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Notifications Push (bêta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Les notifications push sont bloquées. Veuillez les activer dans les paramètres de votre navigateur."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Permettre depuis <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "tout le monde"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "personnes à lesquelles je suis abonné·e"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "abonné⋅es"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Abonnements"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Sondages"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Modifications du message"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "L'autorisation des notifications Push n'a pas été accordée depuis votre dernière connexion. Vous devrez <0><1>vous connecter</1> à nouveau pour accorder l'autorisation Push</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "NOTE : Les notifications Push ne fonctionnent que pour <0>un compte</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:657
#: src/pages/status.jsx:1220
msgid "post.title"
msgstr ""

#: src/pages/status.jsx:911
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Vous n’êtes pas connecté⋅e. Les interactions telles que les réponses et les partages ne sont pas possibles."

#: src/pages/status.jsx:931
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Ce message provient d’une autre instance (<0>{instance}</0>). Les interactions telles que les réponses et les partages ne sont pas possibles."

#: src/pages/status.jsx:959
msgid "Error: {e}"
msgstr "Erreur : {e}"

#: src/pages/status.jsx:966
msgid "Switch to my instance to enable interactions"
msgstr "Basculer vers mon instance pour activer les interactions"

#: src/pages/status.jsx:1068
msgid "Unable to load replies."
msgstr "Les réponses n’ont pas pu être chargées."

#: src/pages/status.jsx:1180
msgid "Back"
msgstr "Retour"

#: src/pages/status.jsx:1211
msgid "Go to main post"
msgstr "Aller au message principal"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1234
msgid "{0} posts above ‒ Go to top"
msgstr "{0} messages plus haut ‒ Remonter"

#: src/pages/status.jsx:1281
#: src/pages/status.jsx:1344
msgid "Switch to Side Peek view"
msgstr "Passer en vue latérale"

#: src/pages/status.jsx:1345
msgid "Switch to Full view"
msgstr "Passer en vue pleine page"

#: src/pages/status.jsx:1363
msgid "Show all sensitive content"
msgstr "Afficher tous les contenus sensibles"

#: src/pages/status.jsx:1368
msgid "Experimental"
msgstr "Expérimental"

#: src/pages/status.jsx:1377
msgid "Unable to switch"
msgstr "Impossible de basculer"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1384
msgid "Switch to post's instance ({0})"
msgstr "Basculer vers l'instance de la publication ({0})"

#: src/pages/status.jsx:1387
msgid "Switch to post's instance"
msgstr "Basculer vers l'instance du message"

#: src/pages/status.jsx:1445
msgid "Unable to load post"
msgstr "Le message n’a pas pu être chargé."

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1581
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# réponse} other {<0>{1}</0> réponses}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1599
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# commentaire} other {<0>{0}</0> commentaires}}"

#: src/pages/status.jsx:1621
msgid "View post with its replies"
msgstr "Voir le message avec ses réponses"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Tendances ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Actualités en tendance"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "Par {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Retour à l'affichage des messages en tendance"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Affichage des messages mentionnant <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Messages en tendance"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Pas de messages en tendance."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Un client Mastodon minimaliste et original."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Se connecter avec Mastodon"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "S’inscrire"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Connectez votre compte Mastodon/Fédivers existant.<0/>Votre mot de passe ne sera pas enregistré sur ce serveur."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Fabriqué</0> par <1>@cheeaun</1>. <2>Politique de confidentialité</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Capture d’écran du carrousel des partages"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Carrousel des partages"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Distingue visuellement les messages de vos abonnements et les messages partagés."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Capture d’écran de commentaires imbriqués"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Commentaires imbriqués"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Facile de suivre les conversations. Commentaires semi-repliables."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Capture d’écran de notifications groupées"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Notifications groupées"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Les notifications similaires sont groupées et réduites pour éviter le désordre."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Capture d’écran de l’interface à plusieurs colonnes"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Une ou plusieurs colonnes"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Une seule colonne par défaut pour une ambiance zen. Plusieurs colonnes configurables pour les plus braves."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Capture d’écran du fil d’actualité à multiples mots-clés, avec un champs pour ajouter des mots-clés"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Fil d’actualité à mots-clés multiples"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Jusqu’à 5 mots-clés combinés dans un seul fil."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Il semblerait que votre navigateur bloque les fenêtres pop-up."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Un message en cours de rédaction est actuellement minimisé. Publiez ou annulez-le avant d’en rédiger un nouveau."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Un message est actuellement en cours de rédaction. Publiez ou annulez-le avant d’en rédiger un nouveau."

