#notifications-page {
  .notification {
    content-visibility: auto;
  }

  .timeline-header {
    position: sticky;
    --mask-padding: 8px;
    top: calc(48px - var(--mask-padding));
    transition: top 0.5s ease-in-out;
    header[hidden] ~ & {
      top: calc(-1 * var(--mask-padding));
    }
    z-index: 1;
    background-color: inherit;
    mask-image: linear-gradient(
      to bottom,
      transparent,
      #000 var(--mask-padding) calc(100% - var(--mask-padding) * 2),
      transparent
    );

    @media (min-width: 40em) {
      background-color: var(--bg-faded-color);
    }
    padding-block: 16px;
    margin: 0;
    opacity: 1;

    #columns & {
      position: static;
      background-color: inherit;
    }

    small {
      font-weight: normal;
      font-size: var(--text-size);
    }
  }
}

.notification {
  display: flex;
  padding: 16px !important;
  gap: 12px;
  animation: appear 0.2s ease-out;
  clear: both;

  b[tabindex='0']:is(:hover, :focus) {
    text-decoration: underline;
    cursor: pointer;
  }
}
.notification-type.notification-mention {
  padding-top: 16px;
}
.only-mentions .notification:not(.notification-mention),
.only-mentions .timeline-empty {
  display: none;
}
.notification.skeleton {
  color: var(--outline-color);
}

.notification-type {
  width: 24px;
  flex-shrink: 0;
  opacity: 0.75;
  color: var(--text-insignificant-color);
  line-height: 0;
}
.notification-type.notification-mention {
  color: var(--reply-to-color);
}
.notification-type.notification-favourite {
  color: var(--favourite-color);
}
.notification-type.notification-reblog {
  color: var(--reblog-color);
}
.notification-type.notification-poll {
  color: var(--link-color);
}

.notification .reblog-icon {
  color: var(--reblog-color);
}
.notification .favourite-icon {
  color: var(--favourite-color);
}

.notification .account-avatar-stack {
  position: relative;
  text-align: center;
}
.notification .account-avatar-stack .account-sub-icons {
  display: block;
  width: fit-content;
  margin: -0.25em auto 0;
  line-height: 1;
  z-index: 1;
  position: relative;
  background-color: var(--bg-blur-color);
  /* background-image: linear-gradient(
    to bottom,
    var(--bg-color),
    var(--bg-blur-color)
  ); */
  padding: 2px 4px;
  border-radius: 999px;
  overflow: hidden;
  border: var(--hairline-width) solid var(--bg-color);
  box-shadow: 0 1px var(--drop-shadow-color);
}
.notification .avatars-stack .account-avatar-stack .account-sub-icons .icon {
  transition: transform 0.2s ease-out;
}
.notification
  .avatars-stack:hover
  .account-avatar-stack
  .account-sub-icons
  .icon {
  transform: rotate(-15deg);
}
.notification
  .avatars-stack:hover
  .account-avatar-stack
  .account-sub-icons
  .icon
  + .icon {
  transform: rotate(15deg);
}

.notification .status-link {
  border-radius: 8px;
  border: 1px solid var(--outline-color);
  max-height: 160px;
  overflow: hidden;
  filter: saturate(0.25);
}
.notification .status-link:not(.status-type-mention) > .status {
  font-size: calc(var(--text-size) * 0.9);
}
.notification .status-link.truncated:not(.status-type-mention) > .status {
  /* fade out mask gradient bottom */
  mask-image: linear-gradient(
    rgba(0, 0, 0, 1) 130px,
    rgba(0, 0, 0, 0.5) 145px,
    transparent 159px
  );
}
.notification .status-link.truncated {
  position: relative;
}
.notification .status-link.truncated:after {
  content: attr(data-read-more);
  line-height: 1;
  display: inline-block;
  position: absolute;
  --inset-offset: 16px;
  inset-block-end: var(--inset-offset);
  inset-inline-end: var(--inset-offset);
  color: var(--text-color);
  background-color: var(--bg-faded-color);
  border: 1px dashed var(--link-color);
  box-shadow:
    0 0 0 1px var(--bg-color),
    0 -5px 10px var(--bg-color),
    0 -5px 15px var(--bg-color),
    0 -5px 20px var(--bg-color);
  padding: 0.5em 0.75em;
  border-radius: 10em;
  font-size: 90%;
  white-space: nowrap;
  text-shadow: 0 -1px var(--bg-color);
  transition: transform 0.2s ease-out;
}
.notification .status-link:is(:hover, :focus).truncated:after {
  color: var(--link-color);
  transform: translate(2px, 0);
}
.notification .status-link.status-type-mention {
  max-height: 320px;
  filter: none;
  background-color: var(--bg-color);
  border-color: var(--reply-to-color);
  box-shadow: 0 0 0 3px var(--reply-to-faded-color);
}
.notification:focus-visible .status-link,
.notification .status-link:is(:hover, :focus) {
  background-color: var(--bg-blur-color);
  filter: saturate(1);
  border-color: var(--outline-hover-color);
}
.notification .status-link.status-type-mention:is(:hover, :focus) {
  border-color: var(--reply-to-color);
  box-shadow: 0 0 5px var(--reply-to-color);
}
.notification .status-link:active {
  filter: brightness(0.95);
}
.notification .status-link > * {
  pointer-events: none;
}

.notification-content {
  flex-grow: 1;
  min-width: 0;
}
.notification-content > p:first-child {
  margin-top: 0;
  margin-bottom: 8px;
  text-wrap: pretty;
}

.notification-group-statuses {
  margin: 0;
  padding: 0;
  list-style: none;
}
.notification-group-statuses > li {
  margin: 0;
  padding: 0;
  list-style: none;
  position: relative;
  counter-increment: index;
}
.notification-group-statuses > li:before {
  content: counter(index);
  position: absolute;
  inset-inline-start: 0;
  font-size: 10px;
  padding: 8px;
  font-weight: bold;
}
.notification-group-statuses > li + li {
  margin-top: -1px;
}
.notification-group-statuses > li:not(:last-child) .status-link {
  border-end-start-radius: 0;
  border-end-end-radius: 0;
}
.notification-group-statuses > li:not(:first-child) .status-link {
  border-start-start-radius: 0;
  border-start-end-radius: 0;
}

#mentions-option {
  position: relative;
  z-index: 2;
  float: right;
  &:dir(rtl) {
    float: left;
  }
  margin-top: 0.5em;
}
#mentions-option label {
  color: var(--text-insignificant-color);
  display: inline-block;
  padding: 1em 16px;
  position: relative;
  cursor: pointer;
  z-index: 1;
  font-size: 90%;
  background-color: var(--bg-blur-color);
  border-radius: 2em;
}
#mentions-option label:has(:checked) {
  color: var(--text-color);
  background-color: var(--bg-color);
}

/* FOLLOW REQUESTS */

.follow-requests {
  padding-block-end: 16px;
}
.follow-requests summary {
  padding: 8px 16px;
  cursor: pointer;
}
.follow-requests summary::marker,
.follow-requests summary::-webkit-details-marker {
  color: var(--text-insignificant-color);
}
.follow-requests details[open] summary {
  color: var(--text-insignificant-color);
}
.follow-requests ul {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 50vh;
  max-height: 50dvh;
  overflow: auto;
  border-bottom: var(--hairline-width) solid var(--outline-color);
}
.follow-requests summary + ul {
  border-top: var(--hairline-width) solid var(--outline-color);
}
.follow-requests ul li {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: var(--hairline-width) solid var(--outline-color);
  justify-content: space-between;
  column-gap: 16px;
  row-gap: 4px;
  flex-wrap: wrap;
}
.follow-requests ul li:last-child {
  border-bottom: none;
}
.follow-requests ul li .follow-request-buttons {
  margin: 0;
  padding: 0;
  display: flex;
  flex: 1;
  gap: 4px;
  justify-content: flex-end;
  align-items: center;
}
.follow-request-buttons .follow-request-states {
  vertical-align: middle;
}
.follow-request-buttons .follow-request-states .icon {
  margin-inline: 8px;
}
.follow-request-buttons .follow-request-states .icon.follow-accepted {
  color: var(--green-color);
}
.follow-request-buttons .follow-request-states .icon.follow-rejected {
  color: var(--red-color);
}
.follow-requests ul li .follow-request-buttons .follow-request-states {
  order: -1;
}

/* ANNOUNCEMENTS */

.announcements {
  border: 1px solid var(--outline-color);
  background-color: var(--bg-blur-color);
  border-radius: 16px;
  margin: 8px;
  overflow: hidden;
}
.announcements summary {
  list-style: none;
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: space-between;
  user-select: none;
  flex-wrap: wrap;
}
.announcements summary .announcement-icon {
  color: var(--red-color);
}
.announcements[open] summary {
  background-color: var(--bg-faded-color);
}
.announcements summary > span {
  display: flex;
  align-items: center;
  gap: 8px;
}
@keyframes wiggle {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-25deg) scale(1.1);
  }
  50% {
    transform: rotate(5deg);
  }
  75% {
    transform: rotate(-15deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
.announcements summary .announcements-nav-buttons {
  transition: all 0.2s ease-in-out;
  opacity: 0;
  pointer-events: none;
  display: none;
}
.announcements[open] summary .announcements-nav-buttons {
  display: flex;
  opacity: 1;
  pointer-events: auto;
}
.announcements summary:hover .announcement-icon {
  animation: wiggle 0.5s 1;
}
.announcements:not([open]):hover {
  background-color: var(--bg-faded-color);
}
.announcements[open] summary {
  color: var(--text-color);
}
.announcements summary::-webkit-details-marker {
  display: none;
}
.announcements > ul {
  display: flex;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-snap-type: x mandatory;
  scroll-behavior: smooth;
  margin: 0;
  padding: 8px;
  gap: 8px;
  background-color: var(--bg-faded-color);
}
.announcements > ul > li {
  background-color: var(--bg-color);
  scroll-snap-align: center;
  scroll-snap-stop: always;
  flex-shrink: 0;
  display: flex;
  width: 100%;
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  border-radius: 8px;
  box-shadow: 0 8px 16px -4px var(--drop-shadow-color);
}
.announcements > ul.announcements-list-multiple > li {
  width: calc(100% - 16px);
}
.announcements > ul > li:last-child {
  border-inline-end: none;
}
.announcements .announcement-block {
  padding: 16px;
  max-height: 50vh;
  max-height: 50dvh;
  overflow: auto;
  mask-image: linear-gradient(
    to top,
    transparent 1px,
    black 48px,
    black calc(100% - 16px),
    transparent calc(100% - 1px)
  );
}
.announcements .announcement-content {
  line-height: 1.4;
}
.announcements .announcement-content p {
  margin-block: min(0.75em, 12px);
  white-space: pre-wrap;
  tab-size: 2;
  text-wrap: pretty;
}
.announcements .announcement-reactions:not(:hidden) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.announcements .announcement-reactions button.reacted {
  color: var(--text-color);
  background-color: var(--link-faded-color);
}

/* FILTERED NOTIFICATIONS */

.filtered-notifications {
  padding-block-end: 16px;

  summary {
    padding: 8px 16px;
    cursor: pointer;
    font-weight: 600;
    user-select: none;
    margin: 16px 0 0;
    color: var(--text-insignificant-color);

    &::marker,
    &::-webkit-details-marker {
      color: var(--text-insignificant-color);
    }
  }
  details[open] summary {
    color: var(--text-color);
  }

  summary + ul {
  }
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 50vh;
    max-height: 50dvh;
    overflow: auto;
    border-top: 1px solid var(--outline-color);
    border-bottom: 1px solid var(--outline-color);
    background-color: var(--bg-faded-color);

    @media (min-width: 40em) {
      background-color: var(--bg-color);
      border-radius: 16px;
      border-width: 0;
    }

    li {
      display: flex;
      padding: 16px;
      row-gap: 8px;
      column-gap: 16px;
      border-bottom: 1px solid var(--outline-color);
    }
    li:last-child {
      border-bottom: none;
    }

    .request-notifcations {
      min-width: 0;
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;

      .last-post {
        max-width: 100%;

        > .status-link {
          border-radius: 8px;
          overflow: hidden;
          --max-height: 160px;
          max-height: var(--max-height);
          border: 1px solid var(--outline-color);

          &:is(:hover, :focus-visible) {
            border-color: var(--outline-hover-color);
          }

          .status {
            mask-image: linear-gradient(
              to bottom,
              black calc(var(--max-height) / 2),
              transparent calc(var(--max-height) - 8px)
            );
            font-size: calc(var(--text-size) * 0.9);

            .content-container {
              pointer-events: none;
              filter: saturate(0.5);
            }
          }
        }
      }

      .request-notifications-account {
        display: flex;
        align-items: center;
        gap: 4px;

        small {
          flex: 1;
          min-width: 0;
        }
      }
    }

    .notification-request-buttons {
      grid-area: buttons;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      gap: 4px;

      button {
        max-width: 30vw;
      }

      .notification-request-states {
        min-height: 32px;
        text-align: center;
        vertical-align: middle;

        .icon {
          margin-inline: 8px;

          &.notification-accepted {
            color: var(--green-color);
          }

          &.notification-dismissed {
            color: var(--red-color);
          }
        }
      }
    }
  }
}

#notifications-settings {
  label {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: space-between;

    input[type='checkbox'] {
      flex-shrink: 0;
    }
  }

  .notification-policy-fields {
    display: flex;
    flex-direction: column;
    gap: 8px;

    select:has(option[value='accept']:checked) {
      box-shadow: 0 0 0 2px var(--green-color);
    }
    select:has(option[value='filter']:checked) {
      box-shadow: 0 0 0 2px var(--orange-color);
    }
    select:has(option[value='drop']:checked) {
      box-shadow: 0 0 0 2px var(--red-color);
    }
  }
}
