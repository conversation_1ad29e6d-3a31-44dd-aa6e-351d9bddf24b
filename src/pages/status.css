.status-deck {
  header {
    white-space: nowrap;
  }
  header h1 {
    min-width: 0;
    flex-grow: 1;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    align-self: stretch;
  }
  header h1 .deck-back {
    margin-inline-start: -16px;
  }

  .button-refresh .icon {
    animation: spin 1s linear;
  }
  .button-refresh:is(:hover, :focus) .icon {
    transition: transform 1s linear;
    transform: rotate(360deg);
  }
}

.hero-heading {
  font-size: var(--text-size);
  display: inline-block;
}
.hero-heading .icon {
  vertical-align: middle;
  color: var(--text-insignificant-color);
}
.hero-heading .insignificant {
  font-weight: normal;
}

.ancestors-indicator {
  font-size: 70% !important;

  & > .avatar ~ .avatar {
    margin-inline-start: -4px;
  }
}
.ancestors-indicator:not([hidden]) {
  animation: slide-up-smooth 0.3s both var(--spring-timing-funtion) 0.3s;
}
.ancestors-indicator[hidden] {
  opacity: 0;
  pointer-events: none;
}

.post-status-banner {
  position: sticky;
  bottom: 16px;
  bottom: max(16px, env(safe-area-inset-bottom));
  font-size: 90%;
  background-color: var(--bg-faded-color);
  padding: 16px;
  margin: 0 16px;
  border-radius: 16px;
  white-space: pre-wrap;
  line-height: 1.2;
  max-width: var(--main-width);
  z-index: 1;
}
.post-status-banner > p:first-of-type {
  margin-top: 0;
  padding-top: 0;
}
